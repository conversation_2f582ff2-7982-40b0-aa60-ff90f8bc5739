import React, { useEffect, useRef, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from '@/context/AuthContext';
import ProtectedRoute from '@/components/common/ProtectedRoute';
import ErrorBoundary from '@/components/common/ErrorBoundary';
import { ToastProvider, setGlobalToastHandler, useToast } from '@/components/common/Toast';
import Login from '@/pages/Login';
import Layout from '@/components/layout/Layout';
import Dashboard from '@/pages/Dashboard';
import Users from '@/pages/Users';
import Chat from '@/pages/Chat';
import System from '@/pages/System';
import Financial from '@/pages/Financial';
import Reports from '@/pages/Reports';
import Logs from '@/pages/Logs';
import AuthTest from '@/pages/AuthTest';
import UpdatePrompt, { shouldShowUpdatePrompt, markUpdatePromptShown } from '@/components/UpdatePrompt';
import { useVersionCheck } from '@/hooks/useVersionCheck';
import { performanceMonitor } from '@/utils/performance';
import '@/utils/authTest'; // 引入认证测试工具
import '@/utils/routerTest'; // 引入Router测试工具
import '@/utils/loginTest'; // 引入登录测试工具
import '@/styles/globals.css';

// 全局Toast处理器设置组件
const ToastHandlerSetup: React.FC = () => {
  const toastContext = useToast();
  const isSetup = useRef(false);

  useEffect(() => {
    if (!isSetup.current) {
      setGlobalToastHandler(toastContext);
      isSetup.current = true;
    }
  }, [toastContext]);

  return null;
};

const App: React.FC = () => {
  // 版本检测状态
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);

  // 使用版本检测Hook
  const {
    versionInfo,
    isChecking,
    error: versionError,
    checkVersion,
    needsUpdate,
    isForceUpdate,
  } = useVersionCheck({
    autoCheck: true,
    checkOnMount: true,
    platform: 'ios', // 管理后台默认使用iOS平台检测
  });

  // 监听版本检测结果
  useEffect(() => {
    if (versionInfo && needsUpdate && shouldShowUpdatePrompt(versionInfo)) {
      setShowUpdatePrompt(true);
      markUpdatePromptShown(versionInfo.latestVersion);
    }
  }, [versionInfo, needsUpdate]);

  useEffect(() => {
    // 初始化性能监控
    performanceMonitor.startTimer('app_initialization');

    // 记录应用启动
    performanceMonitor.incrementCounter('app_start');

    // 监控内存使用
    const memoryInterval = setInterval(() => {
      performanceMonitor.recordMemoryUsage();
    }, 30000); // 每30秒记录一次内存使用

    // 页面卸载时清理
    const handleBeforeUnload = () => {
      performanceMonitor.endTimer('app_initialization');
      performanceMonitor.flush();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      clearInterval(memoryInterval);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      performanceMonitor.destroy();
    };
  }, []);

  // 处理版本更新
  const handleVersionUpdate = () => {
    if (versionInfo?.downloadUrl) {
      window.open(versionInfo.downloadUrl, '_blank');
    }
  };

  // 处理稍后提醒
  const handleVersionLater = () => {
    setShowUpdatePrompt(false);
  };

  // 处理跳过版本
  const handleVersionSkip = () => {
    setShowUpdatePrompt(false);
  };

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        // 记录全局错误
        console.error('Global Error:', error, errorInfo);
        performanceMonitor.incrementCounter('app_error', 1, {
          error: error.name,
          message: error.message,
        });
      }}
    >
      <ToastProvider>
        <Router>
          <AuthProvider>
            <ToastHandlerSetup />
            <div className="App">
              <Routes>
                {/* 登录页面 */}
                <Route path="/login" element={<Login />} />

                {/* 受保护的管理后台路由 */}
                <Route
                  path="/*"
                  element={
                    <ProtectedRoute>
                      <Layout>
                        {/* 版本更新提示 */}
                        {versionInfo && (
                          <UpdatePrompt
                            versionInfo={versionInfo}
                            isOpen={showUpdatePrompt}
                            onClose={() => setShowUpdatePrompt(false)}
                            onUpdate={handleVersionUpdate}
                            onLater={handleVersionLater}
                            onSkip={handleVersionSkip}
                          />
                        )}
                        <Routes>
                          {/* 仪表板 */}
                          <Route path="/" element={<Dashboard />} />
                          <Route path="/dashboard" element={<Navigate to="/" replace />} />
                          <Route path="/admin" element={<Navigate to="/" replace />} />
                          <Route path="/admin/*" element={<Navigate to="/" replace />} />

                          {/* 用户管理 */}
                          <Route path="/users/*" element={<Users />} />

                          {/* 聊天管理 */}
                          <Route path="/chat/*" element={<Chat />} />

                          {/* 财务管理 */}
                          <Route path="/financial/*" element={<Financial />} />

                          {/* 系统管理 */}
                          <Route path="/system/*" element={<System />} />

                          {/* 日志管理 */}
                          <Route path="/logs/*" element={<Logs />} />

                          {/* 统计报表 */}
                          <Route path="/reports/*" element={<Reports />} />

                          {/* 认证测试页面（仅开发环境） */}
                          <Route path="/auth-test" element={<AuthTest />} />

                          {/* 404页面 */}
                          <Route path="*" element={<div>页面未找到</div>} />
                        </Routes>
                      </Layout>
                    </ProtectedRoute>
                  }
                />
              </Routes>
            </div>
          </AuthProvider>
        </Router>
      </ToastProvider>
    </ErrorBoundary>
  );
};

export default App;
