export interface AIServiceConfig {
  _id: string;
  name: string;
  description?: string;
  baseURL: string;
  apiKey: string;
  provider: 'openai' | 'anthropic' | 'google' | 'azure' | 'custom';
  isActive: boolean;
  isDefault: boolean;
  maxRetries: number;
  timeout: number;
  proxyConfig?: {
    enabled: boolean;
    url?: string;
  };
  rateLimits?: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
  createdAt: string;
  updatedAt: string;
  createdBy: {
    _id: string;
    username: string;
    email: string;
  };
  lastUsedAt?: string;
}

export interface AIServiceModel {
  _id: string;
  configId: string;
  modelName: string;
  displayName: string;
  description?: string;
  maxTokens: number;
  supportedFeatures: string[];
  pricing: {
    inputPrice: number;
    outputPrice: number;
    currency: string;
  };
  parameters: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
  };
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface AIConfigUsageStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  totalTokens: number;
  totalCost: number;
  averageResponseTime: number;
}

export interface TestResult {
  success: boolean;
  models?: string[];
  error?: string;
  responseTime?: number;
}

export interface CreateConfigRequest {
  name: string;
  description?: string;
  baseURL: string;
  apiKey: string;
  provider?: 'openai' | 'anthropic' | 'google' | 'azure' | 'custom';
  maxRetries?: number;
  timeout?: number;
  proxyConfig?: {
    enabled: boolean;
    url?: string;
  };
  rateLimits?: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
}

export interface UpdateConfigRequest extends Partial<CreateConfigRequest> {
  isActive?: boolean;
}

export interface ConfigListResponse {
  success: boolean;
  data: {
    configs: AIServiceConfig[];
    total: number;
    page: number;
    limit: number;
  };
}

export interface ConfigResponse {
  success: boolean;
  data: AIServiceConfig;
  message?: string;
}

export interface ModelListResponse {
  success: boolean;
  data: {
    models: AIServiceModel[];
    total: number;
    page: number;
    limit: number;
  };
}

export interface TestResponse {
  success: boolean;
  data: TestResult;
}

export interface UsageStatsResponse {
  success: boolean;
  data: AIConfigUsageStats;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: any[];
}
