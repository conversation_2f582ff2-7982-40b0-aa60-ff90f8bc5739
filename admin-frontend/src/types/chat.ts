import { PaginationParams, SortParams, SearchParams } from './common';

// 聊天消息角色
export type ChatRole = 'user' | 'assistant' | 'other';

// 聊天消息状态
export type MessageStatus = 'sending' | 'sent' | 'receiving' | 'completed' | 'failed';

// 聊天消息类型
export type MessageType = 'text' | 'image' | 'audio' | 'video' | 'file' | 'location' | 'contact' | 'system';

// 完成原因
export type FinishReason = 'stop' | 'length' | 'content_filter' | 'error' | 'no_balance';

// 聊天消息基础结构
export interface ChatMessage {
  _id: string;
  id: string;
  chatId: string;
  role: ChatRole;
  content: string;
  createdTime: string;
  isComplete: boolean;
  messageType?: MessageType;
  finishReason?: FinishReason;
  highlightedContent?: string; // 搜索时的高亮内容
}

// 聊天会话信息
export interface ChatSession {
  chatId: string;
  title?: string;
  messageCount: number;
  firstMessageTime: string;
  lastMessageTime: string;
  userRole?: string;
  assistantRole?: string;
  isArchived?: boolean;
  modelName?: string;
  stepFormId?: string;
}

// 聊天消息列表查询参数
export interface ChatMessageListParams extends PaginationParams, SortParams, SearchParams {
  chatId?: string;
  role?: ChatRole;
  isComplete?: boolean;
  messageType?: MessageType;
  dateFrom?: string;
  dateTo?: string;
}

// 聊天会话列表查询参数
export interface ChatSessionListParams extends PaginationParams, SortParams {
  dateFrom?: string;
  dateTo?: string;
  isArchived?: boolean;
  modelName?: string;
}

// 聊天统计信息
export interface ChatStats {
  totalMessages: number;
  totalSessions: number;
  todayMessages: number;
  todayNewSessions: number;
  messagesByRole: {
    user: number;
    assistant: number;
    other: number;
  };
  messagesByStatus: {
    completed: number;
    incomplete: number;
  };
  messagesByType: {
    text: number;
    image: number;
    audio: number;
    video: number;
    file: number;
    other: number;
  };
  averageMessagesPerSession: number;
  averageSessionDuration: number; // 分钟
  messageTrend: {
    date: string;
    count: number;
  }[];
  sessionTrend: {
    date: string;
    count: number;
  }[];
  popularModels: {
    modelName: string;
    count: number;
    percentage: number;
  }[];
}

// 聊天搜索参数
export interface ChatSearchParams extends PaginationParams {
  keyword: string;
  chatId?: string;
  role?: ChatRole;
  dateFrom?: string;
  dateTo?: string;
}

// 聊天搜索结果
export interface ChatSearchResult {
  messages: ChatMessage[];
  keyword: string;
  total: number;
  page: number;
  limit: number;
  pages: number;
}

// 批量删除参数
export interface BatchDeleteChatParams {
  messageIds?: string[];
  chatId?: string;
  dateFrom?: string;
  dateTo?: string;
  role?: ChatRole;
}

// 聊天导出参数
export interface ChatExportParams {
  format?: 'json' | 'csv';
  chatId?: string;
  dateFrom?: string;
  dateTo?: string;
  role?: ChatRole;
}

// 消息更新数据
export interface MessageUpdateData {
  content?: string;
  isComplete?: boolean;
}

// 聊天分析数据
export interface ChatAnalytics {
  timeRange: {
    start: string;
    end: string;
  };
  metrics: {
    totalMessages: number;
    totalSessions: number;
    averageResponseTime: number; // 毫秒
    completionRate: number; // 百分比
    errorRate: number; // 百分比
  };
  trends: {
    hourlyDistribution: {
      hour: number;
      count: number;
    }[];
    dailyTrend: {
      date: string;
      messages: number;
      sessions: number;
    }[];
    weeklyTrend: {
      week: string;
      messages: number;
      sessions: number;
    }[];
  };
  topChatIds: {
    chatId: string;
    messageCount: number;
    lastActivity: string;
  }[];
}

// 实时聊天监控数据
export interface ChatMonitorData {
  activeSessions: number;
  messagesPerMinute: number;
  averageResponseTime: number;
  errorRate: number;
  onlineUsers: number;
  queuedMessages: number;
  systemLoad: {
    cpu: number;
    memory: number;
    disk: number;
  };
}
