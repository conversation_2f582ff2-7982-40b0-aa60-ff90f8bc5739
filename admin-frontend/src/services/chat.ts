import { request } from './api';
import { 
  ChatMessage, 
  ChatSession,
  ChatMessageListParams, 
  ChatSessionListParams,
  ChatStats, 
  ChatSearchParams,
  ChatSearchResult,
  BatchDeleteChatParams,
  ChatExportParams,
  MessageUpdateData,
  ChatAnalytics
} from '@/types/chat';
import { PaginationResponse } from '@/types/common';

export const chatService = {
  // 获取聊天消息列表
  getMessages: async (params: ChatMessageListParams): Promise<PaginationResponse<ChatMessage>> => {
    const response = await request.get<{ messages: ChatMessage[]; pagination: any }>('/admin/chat/messages', { params });
    if (response.success && response.data) {
      return {
        items: response.data.messages,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '获取聊天消息失败');
  },

  // 获取消息详情
  getMessageById: async (id: string): Promise<ChatMessage> => {
    const response = await request.get<ChatMessage>(`/admin/chat/messages/${id}`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取消息详情失败');
  },

  // 更新消息
  updateMessage: async (id: string, data: MessageUpdateData): Promise<ChatMessage> => {
    const response = await request.put<ChatMessage>(`/admin/chat/messages/${id}`, data);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '更新消息失败');
  },

  // 删除消息
  deleteMessage: async (id: string): Promise<void> => {
    const response = await request.delete(`/admin/chat/messages/${id}`);
    if (!response.success) {
      throw new Error(response.message || '删除消息失败');
    }
  },

  // 批量删除消息
  batchDeleteMessages: async (params: BatchDeleteChatParams): Promise<void> => {
    const response = await request.post('/admin/chat/messages/batch-delete', params);
    if (!response.success) {
      throw new Error(response.message || '批量删除消息失败');
    }
  },

  // 获取聊天会话列表
  getSessions: async (params: ChatSessionListParams): Promise<PaginationResponse<ChatSession>> => {
    const response = await request.get<{ sessions: ChatSession[]; pagination: any }>('/admin/chat/sessions', { params });
    if (response.success && response.data) {
      return {
        items: response.data.sessions,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '获取聊天会话失败');
  },

  // 获取聊天统计信息
  getChatStats: async (params?: { dateFrom?: string; dateTo?: string; groupBy?: 'day' | 'week' | 'month' }): Promise<ChatStats> => {
    const response = await request.get<ChatStats>('/admin/chat/stats', { params });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取聊天统计失败');
  },

  // 搜索消息
  searchMessages: async (params: ChatSearchParams): Promise<ChatSearchResult> => {
    const response = await request.get<ChatSearchResult>('/admin/chat/search', { params });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '搜索消息失败');
  },

  // 导出聊天数据
  exportChatData: async (params: ChatExportParams): Promise<Blob> => {
    const response = await request.get('/admin/chat/export', {
      params,
      responseType: 'blob',
    });
    return response as any; // 这里需要特殊处理blob响应
  },

  // 获取聊天分析数据
  getChatAnalytics: async (params?: { 
    dateFrom?: string; 
    dateTo?: string; 
    groupBy?: 'hour' | 'day' | 'week' | 'month' 
  }): Promise<ChatAnalytics> => {
    const response = await request.get<ChatAnalytics>('/admin/reports/chat', { params });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取聊天分析数据失败');
  },

  // 获取热门聊天话题
  getPopularTopics: async (params?: { 
    limit?: number; 
    dateFrom?: string; 
    dateTo?: string 
  }): Promise<{ topic: string; count: number; percentage: number }[]> => {
    const response = await request.get<{ topic: string; count: number; percentage: number }[]>('/admin/chat/topics', { params });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取热门话题失败');
  },

  // 获取用户聊天统计
  getUserChatStats: async (userId: string): Promise<{
    totalMessages: number;
    totalSessions: number;
    averageMessagesPerSession: number;
    lastChatTime?: string;
    favoriteModel?: string;
  }> => {
    const response = await request.get(`/admin/users/${userId}/chat-stats`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取用户聊天统计失败');
  },

  // 获取模型使用统计
  getModelStats: async (params?: { 
    dateFrom?: string; 
    dateTo?: string 
  }): Promise<{
    modelName: string;
    messageCount: number;
    sessionCount: number;
    averageTokens: number;
    totalCost: number;
  }[]> => {
    const response = await request.get('/admin/chat/model-stats', { params });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取模型统计失败');
  },

  // 获取实时聊天监控数据
  getRealtimeMonitor: async (): Promise<{
    activeSessions: number;
    messagesPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
  }> => {
    const response = await request.get('/admin/chat/monitor');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取实时监控数据失败');
  },
};
