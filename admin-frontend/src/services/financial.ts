import { request } from './api';
import { 
  BalanceTransaction,
  TransactionListParams,
  TransactionStats,
  Product,
  ProductListParams,
  ProductFormData,
  ProductStats,
  Pricing,
  PricingListParams,
  PricingFormData,
  PricingStats,
  BatchProductOperation,
  BatchPricingOperation,
  FinancialOverview,
  RevenueAnalysis,
  ExportParams
} from '@/types/financial';
import { PaginationResponse } from '@/types/common';

export const financialService = {
  // ==================== 交易记录管理 ====================
  
  // 获取交易记录列表
  getTransactions: async (params: TransactionListParams): Promise<PaginationResponse<BalanceTransaction>> => {
    const response = await request.get<{ transactions: BalanceTransaction[]; pagination: any }>('/admin/financial/transactions', { params });
    if (response.success && response.data) {
      return {
        items: response.data.transactions,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '获取交易记录失败');
  },

  // 获取交易记录详情
  getTransactionById: async (id: string): Promise<BalanceTransaction> => {
    const response = await request.get<BalanceTransaction>(`/admin/financial/transactions/${id}`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取交易详情失败');
  },

  // 获取用户交易历史
  getUserTransactions: async (userId: string, params?: Partial<TransactionListParams>): Promise<PaginationResponse<BalanceTransaction>> => {
    const response = await request.get<{ transactions: BalanceTransaction[]; pagination: any }>(`/admin/financial/transactions/user/${userId}`, { params });
    if (response.success && response.data) {
      return {
        items: response.data.transactions,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '获取用户交易历史失败');
  },

  // 获取交易统计信息
  getTransactionStats: async (params?: { 
    dateFrom?: string; 
    dateTo?: string; 
    groupBy?: 'day' | 'week' | 'month' 
  }): Promise<TransactionStats> => {
    const response = await request.get<TransactionStats>('/admin/financial/transactions/stats', { params });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取交易统计失败');
  },

  // 导出交易记录
  exportTransactions: async (params: ExportParams & TransactionListParams): Promise<Blob> => {
    const response = await request.get('/admin/financial/transactions/export', {
      params,
      responseType: 'blob',
    });
    return response as any;
  },

  // ==================== 产品管理 ====================

  // 获取产品列表
  getProducts: async (params: ProductListParams): Promise<PaginationResponse<Product>> => {
    const response = await request.get<{ products: Product[]; pagination: any }>('/admin/financial/products', { params });
    if (response.success && response.data) {
      return {
        items: response.data.products,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '获取产品列表失败');
  },

  // 获取产品详情
  getProductById: async (id: string): Promise<Product> => {
    const response = await request.get<Product>(`/admin/financial/products/${id}`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取产品详情失败');
  },

  // 创建产品
  createProduct: async (data: ProductFormData): Promise<Product> => {
    const response = await request.post<Product>('/admin/financial/products', data);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '创建产品失败');
  },

  // 更新产品
  updateProduct: async (id: string, data: Partial<ProductFormData>): Promise<Product> => {
    const response = await request.put<Product>(`/admin/financial/products/${id}`, data);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '更新产品失败');
  },

  // 删除产品
  deleteProduct: async (id: string): Promise<void> => {
    const response = await request.delete(`/admin/financial/products/${id}`);
    if (!response.success) {
      throw new Error(response.message || '删除产品失败');
    }
  },

  // 批量操作产品
  batchOperateProducts: async (operation: BatchProductOperation): Promise<void> => {
    let endpoint = '';
    let data: any = {};

    switch (operation.operation) {
      case 'enable':
      case 'disable':
        endpoint = '/admin/financial/products/batch-status';
        data = {
          productIds: operation.productIds,
          isEnable: operation.operation === 'enable',
        };
        break;
      case 'delete':
        endpoint = '/admin/financial/products/batch-delete';
        data = { productIds: operation.productIds };
        break;
    }

    const response = await request.post(endpoint, data);
    if (!response.success) {
      throw new Error(response.message || '批量操作失败');
    }
  },

  // 获取产品统计
  getProductStats: async (): Promise<ProductStats> => {
    const response = await request.get<ProductStats>('/admin/financial/products/stats');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取产品统计失败');
  },

  // 复制产品
  duplicateProduct: async (id: string, newProductIdentifier: string): Promise<Product> => {
    const response = await request.post<Product>(`/admin/financial/products/${id}/duplicate`, {
      newProductIdentifier,
    });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '复制产品失败');
  },

  // ==================== 定价管理 ====================

  // 获取定价列表
  getPricings: async (params: PricingListParams): Promise<PaginationResponse<Pricing>> => {
    const response = await request.get<{ pricings: Pricing[]; pagination: any }>('/admin/financial/pricing', { params });
    if (response.success && response.data) {
      return {
        items: response.data.pricings,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '获取定价列表失败');
  },

  // 获取定价详情
  getPricingById: async (id: string): Promise<Pricing> => {
    const response = await request.get<Pricing>(`/admin/financial/pricing/${id}`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取定价详情失败');
  },

  // 创建定价
  createPricing: async (data: PricingFormData): Promise<Pricing> => {
    const response = await request.post<Pricing>('/admin/financial/pricing', data);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '创建定价失败');
  },

  // 更新定价
  updatePricing: async (id: string, data: Partial<PricingFormData>): Promise<Pricing> => {
    const response = await request.put<Pricing>(`/admin/financial/pricing/${id}`, data);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '更新定价失败');
  },

  // 删除定价
  deletePricing: async (id: string): Promise<void> => {
    const response = await request.delete(`/admin/financial/pricing/${id}`);
    if (!response.success) {
      throw new Error(response.message || '删除定价失败');
    }
  },

  // 批量更新定价
  batchUpdatePricing: async (operation: BatchPricingOperation): Promise<void> => {
    const response = await request.post('/admin/financial/pricing/batch-update', operation);
    if (!response.success) {
      throw new Error(response.message || '批量更新定价失败');
    }
  },

  // 获取定价统计
  getPricingStats: async (): Promise<PricingStats> => {
    const response = await request.get<PricingStats>('/admin/financial/pricing/stats');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取定价统计失败');
  },

  // 复制定价
  duplicatePricing: async (id: string, newModelName: string): Promise<Pricing> => {
    const response = await request.post<Pricing>(`/admin/financial/pricing/${id}/duplicate`, {
      newModelName,
    });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '复制定价失败');
  },

  // ==================== 财务概览和分析 ====================

  // 获取财务概览
  getFinancialOverview: async (): Promise<FinancialOverview> => {
    const response = await request.get<FinancialOverview>('/admin/financial/overview');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取财务概览失败');
  },

  // 获取收入分析
  getRevenueAnalysis: async (params?: {
    dateFrom?: string;
    dateTo?: string;
    groupBy?: 'day' | 'week' | 'month';
  }): Promise<RevenueAnalysis> => {
    const response = await request.get<RevenueAnalysis>('/admin/financial/revenue-analysis', { params });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取收入分析失败');
  },
};
