import React from 'react';
import { Routes, Route } from 'react-router-dom';
import FinancialDashboard from './FinancialDashboard';
import TransactionList from './TransactionList';
import ProductList from './ProductList';
import PricingList from './PricingList';

const Financial: React.FC = () => {
  return (
    <Routes>
      <Route index element={<FinancialDashboard />} />
      <Route path="transactions" element={<TransactionList />} />
      <Route path="products" element={<ProductList />} />
      <Route path="pricing" element={<PricingList />} />
    </Routes>
  );
};

export default Financial;
