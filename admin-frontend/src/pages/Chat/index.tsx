import React from 'react';
import { Routes, Route } from 'react-router-dom';
import MessageList from './MessageList';
import SessionList from './SessionList';
import ChatStats from './ChatStats';

const Chat: React.FC = () => {
  return (
    <Routes>
      <Route index element={<MessageList />} />
      <Route path="messages" element={<MessageList />} />
      <Route path="sessions" element={<SessionList />} />
      <Route path="sessions/:id" element={<div>会话详情页面（开发中）</div>} />
      <Route path="stats" element={<ChatStats />} />
    </Routes>
  );
};

export default Chat;
