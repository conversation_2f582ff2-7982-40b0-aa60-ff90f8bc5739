import React, { useState, useEffect } from 'react';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Loading from '@/components/common/Loading';
import { useApi } from '@/hooks/useApi';
import { chatService } from '@/services/chat';
import { ChatStats } from '@/types/chat';

const ChatStatsPage: React.FC = () => {
  const [stats, setStats] = useState<ChatStats | null>(null);
  const [dateRange, setDateRange] = useState({
    dateFrom: format(subDays(new Date(), 30), 'yyyy-MM-dd'),
    dateTo: format(new Date(), 'yyyy-MM-dd'),
    groupBy: 'day' as 'day' | 'week' | 'month',
  });

  // API调用
  const {
    loading,
    execute: fetchStats,
  } = useApi(chatService.getChatStats);

  // 加载统计数据
  const loadStats = async () => {
    try {
      const result = await fetchStats(dateRange);
      setStats(result);
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  };

  // 初始加载
  useEffect(() => {
    loadStats();
  }, []);

  // 处理日期范围变化
  const handleDateRangeChange = (key: string, value: string) => {
    setDateRange(prev => ({ ...prev, [key]: value }));
  };

  // 刷新数据
  const handleRefresh = () => {
    loadStats();
  };

  if (loading && !stats) {
    return <Loading size="lg" text="加载统计数据中..." />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">聊天数据统计</h1>
          <p className="mt-1 text-sm text-gray-600">
            查看聊天消息和会话的统计分析
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="secondary" onClick={handleRefresh} loading={loading}>
            刷新数据
          </Button>
          <Button variant="primary">
            导出报表
          </Button>
        </div>
      </div>

      {/* 时间范围选择 */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Input
            label="开始日期"
            type="date"
            value={dateRange.dateFrom}
            onChange={(e) => handleDateRangeChange('dateFrom', e.target.value)}
          />
          <Input
            label="结束日期"
            type="date"
            value={dateRange.dateTo}
            onChange={(e) => handleDateRangeChange('dateTo', e.target.value)}
          />
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              统计粒度
            </label>
            <select
              value={dateRange.groupBy}
              onChange={(e) => handleDateRangeChange('groupBy', e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="day">按天</option>
              <option value="week">按周</option>
              <option value="month">按月</option>
            </select>
          </div>
          <div className="flex items-end">
            <Button variant="primary" onClick={loadStats} loading={loading}>
              查询
            </Button>
          </div>
        </div>
      </div>

      {stats && (
        <>
          {/* 总体统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">总消息数</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.totalMessages.toLocaleString()}</p>
                  <p className="text-xs text-green-600">今日: {stats.todayMessages}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">总会话数</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.totalSessions.toLocaleString()}</p>
                  <p className="text-xs text-green-600">今日新增: {stats.todayNewSessions}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">平均消息/会话</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.averageMessagesPerSession.toFixed(1)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">平均会话时长</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.averageSessionDuration.toFixed(0)}分钟</p>
                </div>
              </div>
            </div>
          </div>

          {/* 角色分布 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-medium text-gray-900 mb-4">消息角色分布</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">用户消息</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full" 
                        style={{ 
                          width: `${(stats.messagesByRole.user / stats.totalMessages) * 100}%` 
                        }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {stats.messagesByRole.user.toLocaleString()}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">AI助手消息</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full" 
                        style={{ 
                          width: `${(stats.messagesByRole.assistant / stats.totalMessages) * 100}%` 
                        }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {stats.messagesByRole.assistant.toLocaleString()}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">其他消息</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-gray-500 h-2 rounded-full" 
                        style={{ 
                          width: `${(stats.messagesByRole.other / stats.totalMessages) * 100}%` 
                        }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {stats.messagesByRole.other.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-medium text-gray-900 mb-4">消息状态分布</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">已完成</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full" 
                        style={{ 
                          width: `${(stats.messagesByStatus.completed / stats.totalMessages) * 100}%` 
                        }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {stats.messagesByStatus.completed.toLocaleString()}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">未完成</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-yellow-500 h-2 rounded-full" 
                        style={{ 
                          width: `${(stats.messagesByStatus.incomplete / stats.totalMessages) * 100}%` 
                        }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {stats.messagesByStatus.incomplete.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 热门模型 */}
          {stats.popularModels && stats.popularModels.length > 0 && (
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-medium text-gray-900 mb-4">热门模型使用情况</h3>
              <div className="space-y-3">
                {stats.popularModels.slice(0, 5).map((model, index) => (
                  <div key={model.modelName} className="flex justify-between items-center">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-gray-900">#{index + 1}</span>
                      <span className="text-sm text-gray-600">{model.modelName}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-primary-500 h-2 rounded-full" 
                          style={{ width: `${model.percentage}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-900">
                        {model.count.toLocaleString()} ({model.percentage.toFixed(1)}%)
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 趋势图表区域 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">消息趋势</h3>
            <div className="h-64 flex items-center justify-center text-gray-500">
              {/* 这里可以集成图表库，如 Recharts */}
              <p>图表组件待集成（可使用 Recharts 或其他图表库）</p>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ChatStatsPage;
