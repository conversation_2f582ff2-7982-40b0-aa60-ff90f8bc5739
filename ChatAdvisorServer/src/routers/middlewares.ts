import express from 'express';
import logRequestsIntoDB from 'src/middlewares/logHandler';
import errorHandler from 'src/middlewares/errorHandler';
import verifyTokenHandler from 'src/middlewares/verifyTokenHandler';
import decryptionHandler from 'src/middlewares/decryptionHandler';
import logReceiveHandler from 'src/middlewares/printReceiveHandler';
import env from 'src/config/env'; 

const router = express.Router();



// Add conditional middleware based on environment
if (env.isRelease) {
    router.use(logRequestsIntoDB);
} else {
    router.use(logReceiveHandler);
}
router.use(verifyTokenHandler);
router.use(errorHandler);

export default router;
