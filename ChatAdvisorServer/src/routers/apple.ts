import express from 'express';
import appleLogin, {appleAppSiteAssociation} from 'src/business/appleAuth';
import verifyPurchase from 'src/business/verifyPurchase';
import handleRefund from 'src/business/refund';
import errorHandler from 'src/middlewares/errorHandler';


const router = express.Router();

router.get('/apple-app-site-association', appleAppSiteAssociation);
router.post('/appleLogin', appleLogin);
router.post('/verifyPurchase', verifyPurchase);
router.post('/refund', handleRefund);
router.use(errorHandler);

export default router;
