import express from 'express';
import { supportTitle, pricingInfo, contactUs, faqs, exchangeContent, perI18n } from 'src/../locales/support';
import  Pricing  from 'src/models/Pricing';
import { exchangeRate } from 'src/config/env';
import { logger } from 'src/business/logger';
import { privacyPolicy, userTerms } from 'src/../locales/html';
const path = require('path');

const router = express.Router();

// 提供静态文件目录
router.use(express.static(path.join(__dirname, 'public')));

// 路由: 处理 tiktokcalllogin 页面请求
router.get('/tiktoklogin', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'tiktoklogin.html'));
});


router.get('/privacy.html', (req, res) => {
    const lang = req.acceptsLanguages('en', 'zh', 'es') || req.query.lang || 'en'; // 默认语言为英语

    // @ts-ignore
    const privacyPolicyContent = privacyPolicy[lang].privacyPolicy;
    // @ts-ignore
    const lastUpdated = privacyPolicy[lang].lastUpdated;
    // @ts-ignore
    const title = privacyPolicy[lang].title;

    res.render('privacy', { title, privacyPolicyContent, lastUpdated, lang });
});

router.get('/userTerms.html', (req, res) => {
    const lang = req.acceptsLanguages('en', 'zh', 'es') || req.query.lang || 'en'; // 默认语言为英语

    // @ts-ignore
    const userTermsContent = userTerms[lang].userTerms;
    // @ts-ignore
    const lastUpdated = userTerms[lang].lastUpdated;
    // @ts-ignore
    const title = userTerms[lang].title;

    res.render('userTerms', { title, userTermsContent, lastUpdated, lang });
});

router.get('/support.html', async (req, res) => {
    const lang = req.acceptsLanguages('en', 'zh', 'es') || 'en';

    try {
        let pricingData = await Pricing.find({}).lean();
        pricingData.map((item) => {
            // @ts-ignore
            item.count = perI18n[lang].replace('%d', item.count);
        });
        // @ts-ignore
        const pricingContent = pricingInfo[lang];
        // @ts-ignore
        const faqContent = faqs[lang].faqs;
        // @ts-ignore
        const contactUsContent = contactUs[lang].content;
        // @ts-ignore
        const title = supportTitle[lang];
        // @ts-ignore
        const exchangeNote = exchangeContent[lang] + exchangeRate + ' ';

        if (!pricingContent) {
            throw new Error(`Missing pricing content for language: ${lang}`);
        }
        res.render('support', {
            title,
            pricingData,
            faqContent,
            contactUsContent,
            pricingContent,
            lang,
            exchangeRate,
            exchangeNote,
        });
    } catch (error) {
        console.error('Error fetching pricing data:', error);
        res.status(500).send('Internal Server Error');
    }
});

export default router;
