import mongoose,{ Schema, model, Model } from 'mongoose';

interface IConfig {
  privacyPolicy: string;
  termsOfService: string;
  appVersion: string;
  supportEmail: string;
  featureFlags: { [key: string]: boolean };
  mainSolgan: { [language: string]: string[] };
  registerSolgan: { [language: string]: string[] };
  emailLoginSolgan: { [language: string]: string[] };
  rechargeMessages: { [language: string]: string[] };
  hideMessage: { [language: string]: string[] };
  rechargeDescription: { [language: string]: string };
  promotLocal: { [language: string]: string };
  promotCloud: { [language: string]: string };
  compressRate: number;
}

const configSchema = new Schema<IConfig>({
  privacyPolicy: { type: String, required: true },
  termsOfService: { type: String, required: true },
  appVersion: { type: String, required: true },
  supportEmail: { type: String, required: true },
  featureFlags: { type: Map, of: Boolean, required: true },
  mainSolgan: { type: Map, of: [String], required: true },
  registerSolgan: { type: Map, of: [String], required: true },
  emailLoginSolgan: { type: Map, of: [String], required: true },
  rechargeMessages: { type: Map, of: [String], required: true },
  hideMessage: { type: Map, of: [String], required: true },
  rechargeDescription: { type: Map, of: String, required: true },
  promotCloud: { type: Map, of: String, required: false },
  promotLocal: { type: Map, of: String, required: false },
  compressRate: { type: Number, required: true }
},{
  versionKey: false
});

const modelName = 'Config';


const Config: Model<IConfig>  = mongoose.models[modelName] || model<IConfig>(modelName, configSchema);

export { Config };
