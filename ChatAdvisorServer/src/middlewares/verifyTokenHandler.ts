// import
import e, { Request, Response, NextFunction } from 'express';
import env, { secretKey } from 'src/config/env';
import jwt from 'jsonwebtoken';
import User from 'src/models/User';
import { logger } from 'src/business/logger';
import { HttpStatusCode } from 'axios';

// 不需要校验的路径
const notVerify = [
    '/appleLogin',
    '/twitterLogin',
    '/facebookLogin',
    '/deletion',
    '/verify-deletion',
    '/facebook-data-deletion',
    '/generateQuestion',
    '/getConfig',
    '/verifyPurchase',
    '/refund',
    '/login',
    '/getProducts',
    '/getPricing',
    '/favicon.ico',
    '/verifyEmailCode',
    '/sendEmailCode',
    '/googleLogin',
    '/limit',
    '/apple-app-site-association',
    '/tiktokLogin', // 登录用的接口
    '/tiktoklogin', // tiktok回调的接口
]

// Token校验中间件
const verifyTokenHandler = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // 提取token
        // html 不需要校验
        if (req.url.includes('.html')) {
            next();
            return;
        }

        // 如果在notVerify数组中存在则不需要校验
        if (notVerify.includes(req.path)) {
            next();
            return;
        }

        if (token == null) {
            // console.log('Token is required!');
            next({ code: HttpStatusCode.Forbidden, message: req.t('token_required') });
            return;
        }

        const decoded = jwt.verify(token, secretKey);
        // decoded to json
        logger.silly('decoded: ', decoded);
        // @ts-ignore
        // 判断token中的userId在数据库是否存在
        if (decoded && decoded.userId) {
            // @ts-ignore
            let user = await User.findById(decoded.userId).lean();
            // logger.silly('userEmail : ', user?.email);
            if (!user) {
                next({ code: HttpStatusCode.Unauthorized, message: req.t('unauthorized') });
                return;
            }
        } else {
            next({ code: HttpStatusCode.Unauthorized, message: req.t('unauthorized') });
        }
        // @ts-ignore
        req.token = decoded;
        // @ts-ignore
        logger.debug('req.token: ', req.token);
        next();
    } catch (error) {
        next({ code: HttpStatusCode.Unauthorized, message: req.t('unauthorized') });
    }
};

export default verifyTokenHandler;
