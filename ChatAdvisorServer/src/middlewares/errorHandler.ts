import { ErrorRequestHandler } from 'express';
import ErrorLog from 'src/models/ErrorLog';
import env from 'src/config/env';
import { logger } from 'src/business/logger';
import { HttpStatusCode } from 'axios';

const errorHandler: ErrorRequestHandler = (err, req, res, next) => {
    logger.error(`path: ${req.path}, error: ${err.message}, code: ${err.code || err.status || err.statusCode || 500}`);
    // 需要处理code 不是数字的情况，可能是非数字的字符串
    if (typeof err.code === 'string') {
        err.code = parseInt(err.code) || HttpStatusCode.InternalServerError;
    }
    if (env.isRelease) {
        // 创建错误日志记录
        const errorLogEntry = new ErrorLog({
            // @ts-ignore
            requestId: req.logId,
            error: err.message,
            errorCode: err.status || err.statusCode || 500,
            stack: err.stack
        });

        // 保存错误日志到数据库
        errorLogEntry.save();
    }

    if (req.url == '/refund') {
        // 返回错误响应
        const statusCode = err.code || err.status || err.statusCode || HttpStatusCode.InternalServerError; // 默认状态码为500
        const errorMessage = err.message || req.t('internal_server_error'); // 使用国际化字符串作为默认错误消息
        res.status(statusCode).send({ code: statusCode, message: errorMessage, data: null });
    } else {
        // 返回错误响应
        const statusCode = err.code || err.status || err.statusCode || HttpStatusCode.InternalServerError; // 默认状态码为500
        const errorMessage = err.message || req.t('internal_server_error'); // 使用国际化字符串作为默认错误消息
        res.status(200).send({ code: statusCode, message: errorMessage, data: null });
        logger.error(`path: ${req.path}, error: ${err.message}`);
    }
};

export default errorHandler;
