// middleware/logRequests.ts
import { Request, Response, NextFunction } from 'express';
import RequestLog from 'src/models/RequestLog';
import ResponseLog from 'src/models/ResponseLog';
import { DateTime } from 'luxon';

const logRequestsIntoDB = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const start = process.hrtime();
        const requestLogEntry = new RequestLog({
            url: req.originalUrl,
            method: req.method,
            requestBody: req.body,
            requestTime: DateTime.now().setZone('Asia/Shanghai')
        });

        const savedRequestLog = await requestLogEntry.save();
        // @ts-ignore
        req.logId = savedRequestLog._id;

        const originalSend = res.send;
        // @ts-ignore
        res.send = function (body: any) {
            const durationInMilliseconds = process.hrtime(start)[1] / 1000000;
            const responseLogEntry = new ResponseLog({
                requestBody: req.body,
                requestLogId: savedRequestLog._id,
                responseBody: body,
                responseStatus: res.statusCode,
                responseTime: new Date(Date.now() + durationInMilliseconds)
            });

            responseLogEntry.save().catch(err => console.error('Error saving response log:', err));

            // 注意：这里我们不再使用.bind(res)和async/await
            // 我们直接调用originalSend，以避免更改其上下文或返回值
            originalSend.call(res, body);
        };
    } catch (error) {
        console.error('Error saving request log:', error);
    }
    // console.log('logRequestsIntoDB Received request:', req.body);
    // 继续执行下一个中间件
    next();
};

export default logRequestsIntoDB;
