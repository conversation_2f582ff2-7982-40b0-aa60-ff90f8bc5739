import crypto from 'crypto';
import { MongoDB } from 'winston-mongodb';
import { MONGODB_URI } from 'src/config/database';
import { logger } from 'src/business/logger';

// 判断环境
const env = {
    isProduction: process.env.NODE_ENV === 'production',
    isDevelopment: process.env.NODE_ENV === 'development',
    isTest: process.env.NODE_ENV === 'test',
    isRelease: process.env.NODE_ENV === 'production'
};
// 加密
export const needToEncrypt = 0;
// 端口
export const port = process.env.PORT

// 秘钥
const originalSecretKey = 'BIryQ4iRqJo1NqwJzJbMvTShcU6Iz4/bY+YYbcw/+J0=';
// 使用相同的编码格式和哈希算法
const hash = crypto.createHash('sha256');
hash.update(originalSecretKey);
export const secretKey = hash.digest('hex');

// token有效期
export const tokenExpiresIn = 7 * 24 * 60 * 60;

// 创建用户时给的默认余额
export const defaultBalance = 50;

export const ENV_MONGODB_URI = env.isRelease
    ? 'mongodb://localhost:27017/ChatAdvisor?replicaSet=rs0'
    : 'mongodb://localhost:27017/ChatAdvisor_test?replicaSet=rs0'

// 利润
export const profitRate = 0.045;
// 汇率
export const exchangeRate = 100;

export const googleClientId = '873925125344-hktsn356sutu326jf1el2td6lfsokbdf.apps.googleusercontent.com';

export const twitterApiKey = "*************************"
export const twitterApiSecret = "TmMqViKTVqgFAfTRwGNuXcKzbulPH7h4dPtUtudKE2DJyEJgUu"

export const tiktokClientId = 'awk9nx195kycy67u' 
export const tiktokClientSecret = 'J7kZUogyDrqt4qIEQDjInGd9B2pbrkOr'


export default env;

