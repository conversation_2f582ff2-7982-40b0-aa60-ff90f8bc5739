import mongoose from 'mongoose';
import env, { ENV_MONGODB_URI } from 'src/config/env';

export const MONGODB_URI = process.env.MONGODB_URI || ENV_MONGODB_URI;
import { exec } from 'child_process';
import { existsSync, copyFileSync } from 'fs';
import { resolve } from 'path';

// 当前目录下的 mongod.conf 文件路径
const currentConfigPath = process.cwd() + '/mongod.conf';
// MongoDB 默认配置文件路径
const defaultConfigPath = '/usr/local/etc/mongod.conf';

// 检查当前操作系统是否为 macOS
const isMacOS = process.platform === 'darwin';

// 重启 MongoDB 服务
function restartMongoDB() {
    const restartCommand = isMacOS ? 'brew services restart mongodb-community' : 'sudo systemctl restart mongod';
    exec(restartCommand, (error, stdout, stderr) => {
        if (error) {
            console.error('Failed to restart MongoDB:', error.message);
            return;
        }
        console.log('MongoDB restarted successfully');
        // 等待服务完全启动后初始化副本集
        setTimeout(() => {
            initiateReplicaSet();
        }, 3000);
    });
}

// 初始化副本集
function initiateReplicaSet() {
    const initCommand = 'mongosh --eval "rs.initiate()"';
    exec(initCommand, (error, stdout, stderr) => {
        if (error) {
            // 如果副本集已经存在，这是正常的
            if (error.message.includes('already initialized') || stderr.includes('already initialized')) {
                console.log('Replica set already initialized');
                return;
            }
            console.error('Failed to initiate replica set:', error.message);
            return;
        }
        console.log('Replica set initiated successfully');
        console.log(stdout);
    });
}

const connectDatabase = async (): Promise<void> => {
    try {
        await mongoose.connect(MONGODB_URI);
        // console.log('Successfully connected to MongoDB.');
    } catch (error) {
        console.error('Error connecting to MongoDB:', error);
        process.exit(1);
    }
};

// 检查MongoDB服务状态
function checkMongoDBStatus() {
    const checkCommand = isMacOS ? 'brew services list | grep mongodb' : 'systemctl is-active mongod';
    exec(checkCommand, (error, stdout, stderr) => {
        if (error || !stdout.includes('started')) {
            console.log('MongoDB service is not running, attempting to start...');
            startMongoDB();
        } else {
            console.log('MongoDB service is already running');
            initiateReplicaSet();
        }
    });
}

// 启动MongoDB服务
function startMongoDB() {
    const startCommand = isMacOS ? 'brew services start mongodb-community' : 'sudo systemctl start mongod';
    exec(startCommand, (error, stdout, stderr) => {
        if (error) {
            console.error('Failed to start MongoDB:', error.message);
            return;
        }
        console.log('MongoDB service started successfully');
        // 等待服务完全启动后初始化副本集
        setTimeout(() => {
            initiateReplicaSet();
        }, 3000);
    });
}

const main = async () => {
    console.log('Setting up MongoDB replica set...');

    // 如果当前目录下的 mongod.conf 与默认配置文件不一致且存在，则拷贝当前目录下的配置文件到默认路径
    if (existsSync(currentConfigPath) && currentConfigPath !== defaultConfigPath) {
        try {
            copyFileSync(currentConfigPath, defaultConfigPath);
            console.log('MongoDB configuration file copied successfully');
            // 执行重启 MongoDB 服务操作
            restartMongoDB();
        } catch (error) {
            console.error('Failed to copy config file:', error);
            // 如果复制失败，直接检查服务状态
            checkMongoDBStatus();
        }
    } else {
        console.log('Using existing MongoDB configuration');
        // 直接检查MongoDB服务状态
        checkMongoDBStatus();
    }
};

setTimeout(() => {
    main()
}, 5000); // 延迟 5 秒执行副本集初始化和添加成员操作

export default connectDatabase;
