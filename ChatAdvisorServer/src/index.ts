import express from 'express';
import { port } from 'src/config/env';
import { logger } from 'src/business/logger';
import { engine } from 'express-handlebars';
import { ensureData } from 'src/business/ensureData';
import connectDatabase from 'src/config/database';
import BaseRouter from 'src/routers/base';
import AppleRouter from 'src/routers/apple';
import GoogleRouter from 'src/routers/google';
import TikTokRouter from 'src/routers/tiktok';
import TwitterRouter from 'src/routers/twitter';
import FacebookRouter from 'src/routers/facebook';
import AuthRouter from 'src/routers/auth';
import ChatRouter from 'src/routers/chat';
import UploadRouter from 'src/routers/upload';
import SupportRouter from 'src/routers/support';
import ConfigRouter from 'src/routers/config';
import MiddlewaresRouter from 'src/routers/middlewares';
const rootDir = process.cwd();
import 'dotenv/config';

const app = express();
app.use(express.static(rootDir + '/src/public'));
app.engine('handlebars', engine());
app.set('view engine', 'handlebars');
app.set('views', rootDir + '/src/views');
 
connectDatabase().then(() => {
    ensureData();

    app.use(BaseRouter);
    app.use(MiddlewaresRouter);
    app.use(ConfigRouter);
    app.use(AppleRouter);
    app.use(GoogleRouter);
    app.use(TikTokRouter);
    app.use(TwitterRouter);
    app.use(FacebookRouter);
    app.use(AuthRouter);
    app.use(ChatRouter);
    app.use(UploadRouter);
    app.use(SupportRouter);

    app.listen(port, () => {
        logger.debug(`Server started on port ${port}`);
    });
});
