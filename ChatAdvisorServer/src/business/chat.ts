import { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';
import OpenAI from 'openai';
import Pricing from 'src/models/Pricing';
import UserBalance from 'src/models/Balance';
import { ChatMessageModel, Role } from 'src/models/ChatMessage';
import BalanceTransaction, { TransactionType } from 'src/models/BalanceTransaction';
import env from 'src/config/env';
import { randomUUID } from 'crypto';
import { logger } from 'src/business/logger';
import { HttpStatusCode } from 'axios';
import { encoding_for_model, TiktokenModel } from 'tiktoken';

const openai = new OpenAI({
    apiKey: 'sk-06008ac4280e400badd4d4ee0e094a15',
    baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1'
});

async function chatWithOpenai(req: Request, res: Response, next: NextFunction) {
    let uuid = randomUUID();
    const noBalanceResponse = [
        {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        content: req.t('no_balance_1')
                    },
                    logprobs: null,
                    finish_reason: "no_balance"
                }
            ]
        },
        {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        content: req.t('no_balance_2')
                    },
                    logprobs: null,
                    finish_reason: 'no_balance'
                }
            ]
        }
    ];
    const appVersionFilter = 103
        // 从请求头App-Version获取版本号
        // @ts-ignore
        const appVersion = req.headers['app-version'];
        // 1.1.3开始客户端调整了数据的方式,增加识别data:\n\n
        // @ts-ignore
        const appVersionNum = parseInt(appVersion.replace(/\./g, ''));

    const session = await mongoose.startSession();
    session.startTransaction();

    try {
        // @ts-ignore
        const userId = req.token.userId; // 从 token 中获取用户 ID
        const { modelname, chatid, 'remote-recognize': remote_recognize } = req.headers;

        const modelPrice = await Pricing.findOne({ modelName: modelname });
        if (!modelPrice) {
            throw new Error('internal_server_error');
        }

        const modelName = modelPrice.modelName as TiktokenModel;
        // gpt-4o 和 gpt-4o-mini 使用相同的编码
        const encoding = encoding_for_model("gpt-4o");
        

        let tokenLength = 0;
        if (remote_recognize === '1') {
            logger.debug("remote-recognize", remote_recognize);
            req.body.messages.forEach((msg: any) => {
                if (msg.role === 'system') {
                    tokenLength += encoding.encode(msg.content).length;
                } else if (msg.role === 'user') {
                    const totalCost = msg.total_token_cost;
                    const contentCost = msg.content.reduce((acc: number, item: any) => {
                        if (item.type === 'image_url') {
                            if (item.token_cost != calculateImageTokens(item.width, item.height, "low")) {
                                return -1
                            }
                            return acc + item.token_cost;
                        }
                        return acc;
                    }, 0);
                    if (totalCost !== contentCost) {
                        throw new Error('token_cost_mismatch');
                    }
                    const testCost = msg.content.reduce((acc: number, item: any) => {
                        if (item.type === 'text') {
                            return acc + encoding.encode(item.text).length;
                        }
                        return acc;
                    }, 0);
                    tokenLength += contentCost;
                    tokenLength += testCost;
                }
            });
        } else {
            tokenLength = req.body.messages.reduce((acc: number, msg: any) => acc + encoding.encode(msg.content).length, 0);
        }

        const consumption = parseFloat(((tokenLength / modelPrice.count) * modelPrice.inPrice).toFixed(4));
        const userBalance = await UserBalance.findOne({ userId }).session(session);

        if (!userBalance) {
            throw new Error('internal_server_error');
        }

        if (userBalance.balance < consumption) {
            logger.debug("余额不足");
            for (const item of noBalanceResponse) {
                res.write(`data: ${JSON.stringify(item)}\n\n`);
            }
            res.end();
            return;
        }

        const updatedBalance = await UserBalance.findOneAndUpdate(
            { userId, balance: userBalance.balance },
            { $inc: { balance: -consumption } },
            { new: true }
        ).session(session);
        if (!updatedBalance) {
            throw new Error('internal_server_error');
        }

        const balanceTransaction = new BalanceTransaction({
            userId,
            amount: -consumption,
            reason: 'Chat with OpenAI',
            timestamp: Date.now(),
            modelId: modelPrice._id,
            type: TransactionType.In
        });
        await balanceTransaction.save({ session });

        await session.commitTransaction();
        session.endSession();

        // 数据清理
        const cleanedMessages = req.body.messages.map((msg: any) => {
            const { content, role } = msg;
            if (role === 'user' && msg.content && remote_recognize === '1') {
                const cleanedContent = msg.content.map((item: any) => {
                    if (item.type === 'image_url') {
                        // 仅保留 image_url 的 detail 和 url
                        return { type: 'image_url', image_url: item.image_url };
                    }
                    return item;
                });
                return { content: cleanedContent, role };
            }
            if(role === 'system') {
                return { content: `You are a smart messaging assistant. The messages I provide are sent to me by someone else (the target). Your job is to reply to these messages in my voice, as if I am responding directly, based on the conversation history and  ## Context:${content}. I should be able to copy and paste your response to reply to the target naturally, like a real person would.

## Your Task
1. Review the conversation history to understand my relationship with the target and our communication style.
2. Respond in my first-person perspective (e.g., "I think...", "I’ll check..."), matching my tone and style from the history.
3. Ensure the reply is contextually relevant, directly addresses the message sent to me, and moves the conversation forward naturally without ending in questions unless absolutely necessary.
4. Adjust the tone based on the target and their mood (e.g., formal for a boss, playful for a flirt, casual for a friend, or sharper if they’re annoyed).
5. Keep the response engaging, human-like, and ready for me to copy and paste, avoiding robotic repetition, excessive questions, or a passive "waiting-for-instruction" tone.
6. Use the ## Context:${content} as a loose guide to shape the conversation, but adapt flexibly to the target's responses—don’t stick rigidly to one idea or topic.
7. If the target seems bored, impatient, or pushes for something else, shift gears quickly with humor, exaggerated devotion, or a direct approach, staying true to my intent while embracing their lead.
8. Take the lead when appropriate, offering eager suggestions or playful remarks that reflect the reply emotion defined in ## Context: ${content}—don’t just wait for their input, but don’t ignore their clear desires either.
9. Never include anything inconsistent with the conversation history or my intent.`, role };
            }
            return { content, role };
        });

        let fullResponse = '';
        let messageId = '';
        logger.debug("cleanedMessages", cleanedMessages);
        const stream = openai.beta.chat.completions.stream({
            model: 'qwen-plus',
            stream: true,
            messages: cleanedMessages,
            frequency_penalty: 1.2,   // 提高到 1.2，进一步减少重复用词
            temperature: 0.9,         // 调整到 0.9，平衡创造性与连贯性
            presence_penalty: 1.2,    // 提高到 1.2，鼓励新话题和角度
        //     enable_thinking:false
        });
        // @ts-ignore
        for await (const chunk of stream.toReadableStream()) {
            const chunkString = Buffer.from(chunk).toString('utf-8');
            // logger.debug(`data: ${chunkString}\n\n`);
            try {
                const chunkObject = JSON.parse(chunkString);
                const deltaContent = chunkObject.choices[0].delta.content;
                messageId = chunkObject.id;
                if (deltaContent) {
                    fullResponse += deltaContent;
                }
                if (appVersionNum >= appVersionFilter) {
                    res.write(`data: ${chunkString}\n\n`);
                    // logger.debug(`data: ${chunk}\n\n`);
                } else {
                    res.write(chunk);
                }
            } catch (error) {
                logger.error(`Error parsing JSON: ${error}`);
            }
        }
        // logger.debug("fullResponse", fullResponse);
        const responseLength = encoding.encode(fullResponse).length;
        logger.debug("responseLength", responseLength);
        const responseConsumption = parseFloat(((responseLength / modelPrice.count) * modelPrice.outPrice).toFixed(4));
        const responseUpdatedBalance = await UserBalance.findOneAndUpdate(
            { userId },
            { $inc: { balance: -responseConsumption } },
            { new: true }
        );
        if (!responseUpdatedBalance) {
            throw new Error('internal_server_error');
        }

        const responseBalanceTransaction = new BalanceTransaction({
            userId,
            amount: -responseConsumption,
            reason: 'Chat with OpenAI',
            timestamp: Date.now(),
            modelId: modelPrice._id,
            type: TransactionType.Out
        });
        await responseBalanceTransaction.save();

        if (fullResponse !== '') {
            await ChatMessageModel.create({
                id: messageId,
                chatId: chatid,
                createdTime: Date.now(),
                role: Role.ASSISTANT,
                content: fullResponse,
                isComplete: true
            });
        }
        res.end();
    } catch (e) {
        logger.error('catch error:', e);
        await session.abortTransaction();
        const errorResponse = {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        // @ts-ignore
                        content: e.message || req.t('internal_server_error')
                    },
                    logprobs: null,
                    finish_reason: 'error'
                }
            ]
        };
        res.write(`data: ${JSON.stringify(errorResponse)}\n\n`);
        res.end();
    } finally {
        if (session.inTransaction()) {
            await session.abortTransaction();
        }
        session.endSession();
    }
}

/**
 * 计算图像的token成本
 * @param width - 图像的宽度
 * @param height - 图像的高度
 * @param detail - 图像的详细程度 ('low' 或 'high')
 * @returns 返回图像的token成本
 */
function calculateImageTokens(width: number, height: number, detail: 'low' | 'high'): number {
    // detail: low 模式下，固定成本为 85 tokens
    if (detail === 'low') {
        return 85;
    }

    // 确保图像适应2048x2048的范围
    if (width > 2048 || height > 2048) {
        const scalingFactor = Math.min(2048 / width, 2048 / height);
        width = Math.floor(width * scalingFactor);
        height = Math.floor(height * scalingFactor);
    }

    // 确保图像的最短边为768px长
    if (width < 768 || height < 768) {
        const scalingFactor = Math.max(768 / width, 768 / height);
        width = Math.floor(width * scalingFactor);
        height = Math.floor(height * scalingFactor);
    }

    // 计算512px正方形的数量
    const numTiles = Math.ceil(width / 512) * Math.ceil(height / 512);

    // detail: high 模式下，成本为170 tokens每个512px正方形，加上85 tokens的固定成本
    return numTiles * 170 + 85;
}

export default chatWithOpenai;
