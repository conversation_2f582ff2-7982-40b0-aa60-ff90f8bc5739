import { Request, Response, NextFunction } from 'express';
import { secretKey, tokenExpiresIn, googleClientId } from 'src/config/env';
import { OAuth2Client } from 'google-auth-library';
import jwt from 'jsonwebtoken';
import User from 'src/models/User';
import { logger } from './logger';

// 初始化 Google OAuth2 Client
const client = new OAuth2Client(googleClientId);

// 验证 Google 登录的 idToken
export async function verifyGoogleIdToken(idToken: string, audience: string): Promise<any> {
    const ticket = await client.verifyIdToken({
        idToken,
        audience: googleClientId
    });
    const payload = ticket.getPayload();
    if (!payload) {
        throw new Error('Invalid Google ID token');
    }
    return payload;
}

// Google 登录校验逻辑
const googleLogin = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { idToken } = req.body;

        // 验证 Google 登录的 idToken
        const googleInfo = await verifyGoogleIdToken(idToken, googleClientId);

        // 检查解码后的信息是否有效
        if (!googleInfo || !googleInfo.email) {
            throw new Error('Invalid Google token');
        }

        // 查找邮箱是否存在
        const existingUser = await User.findOne({ email: googleInfo.email });

        if (existingUser) {
            // 检查用户是否已绑定此 Google 账号
            if (existingUser.externalAccounts && existingUser.externalAccounts.googleInfo && existingUser.externalAccounts.googleInfo.sub === googleInfo.sub) {
                // 更新 isDelete 状态
                await User.updateOne({ email: googleInfo.email }, { isDelete: false });
                // 生成 JWT Token
                const token = jwt.sign({ userId: existingUser._id, email: existingUser.email }, secretKey, { expiresIn: tokenExpiresIn });
                res.status(200).json({
                    message: 'Google login successful',
                    code: 200,
                    data: { ...existingUser.toObject(), token }
                });
            } else {
                // 存在邮箱但未绑定此 Google 账号，提示邮箱冲突
                res.status(200).json({
                    message: req.t('email_conflict'),
                    code: 409,
                });
            }
        } else {
            // 创建新用户
            const newUser = new User({
                email: googleInfo.email,
                externalAccounts: {
                    googleInfo
                }
            });
            await newUser.save();
            const token = jwt.sign({ userId: newUser._id, email: newUser.email }, secretKey, { expiresIn: tokenExpiresIn });
            res.status(200).json({
                message: 'Google login successful',
                code: 200,
                data: { ...newUser.toObject(), token }
            });
        }
    } catch (error: any) {
        console.log(error);
        // 处理错误情况，返回相应的错误响应
        next(error);
    }
};

export default googleLogin;
