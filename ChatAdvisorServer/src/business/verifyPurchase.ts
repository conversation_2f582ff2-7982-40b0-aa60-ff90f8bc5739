import { Request, Response, NextFunction } from 'express';
import UserBalance from 'src/models/Balance';
import VerificationResult from 'src/models/VerificationResult';
import UserVerification from 'src/models/UserVerification';
import { Product } from 'src/models/Product'; // 导入Product模型
import fetch from 'node-fetch';
import env from 'src/config/env';
import { HttpStatusCode } from 'axios';
import { logger } from 'src/business/logger';

interface PurchaseInfo {
    userId: string;
    receipt: string;
    productIdentifier: string; // 修改为productIdentifier
}

async function verifyApplePurchase(purchaseInfo: PurchaseInfo): Promise<any> {
    const requestBody = {
        'receipt-data': purchaseInfo.receipt
    };

    // Initial validation URL set to production
    let validationUrl = 'https://buy.itunes.apple.com/verifyReceipt';
    let response = await fetch(validationUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
    });
    logger.http('verifyApplePurchase response', response)
    let responseBody = await response.json();
    // If the response indicates a sandbox receipt, change URL and retry
    if (responseBody.status === 21007) {
        logger.http('Sandbox receipt, retrying with sandbox URL');
        validationUrl = 'https://sandbox.itunes.apple.com/verifyReceipt';
        response = await fetch(validationUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        responseBody = await response.json();
    }

    logger.info('responseBody', responseBody);
    return responseBody;
}

async function verifyPurchase(req: Request, res: Response, next: NextFunction) {
    try {
        const purchaseInfo: PurchaseInfo = req.body;
        logger.http('purchaseInfo', purchaseInfo);
        // 获取对应productIdentifier的amount
        const product = await Product.findOne({ productIdentifier: purchaseInfo.productIdentifier });
        if (!product) {
            return next({ message: req.t('data_not_found'), code: HttpStatusCode.NotFound });
        }
        const amount = product.amount;

        const responseBody = await verifyApplePurchase(purchaseInfo);

        if (responseBody.status !== 0) {
            return next({ message: req.t('invalid_apple_purchase'), code: HttpStatusCode.BadRequest });
        }

        const verificationResult = new VerificationResult({
            receipt: responseBody.receipt,
            environment: responseBody.environment,
            status: responseBody.status
        });
        await verificationResult.save();

        const userVerification = new UserVerification({
            userId: purchaseInfo.userId,
            verificationResult: verificationResult._id,
            amount: amount // 使用从Product获取的amount
        });
        await userVerification.save();

        const userBalance = await UserBalance.findOneAndUpdate({ userId: purchaseInfo.userId }, { $inc: { balance: amount } }, { new: true });

        if (!userBalance) {
            return next({ message: req.t('data_not_found'), code: HttpStatusCode.NotFound });
        }

        return res.status(HttpStatusCode.Ok).json({
            message: 'success',
            code: HttpStatusCode.Ok,
            data: userBalance.balance
        });
    } catch (error) {
        console.error(error);
        next({ message: req.t('internal_server_error'), code: HttpStatusCode.InternalServerError });
    }
}

export default verifyPurchase;
