import { Request, Response, NextFunction } from 'express';
import UserBalance from 'src/models/Balance';
import UserVerification from 'src/models/UserVerification';
import { HttpStatusCode } from 'axios';
import { logger } from 'src/business/logger';
import jwt from 'jsonwebtoken';
import jwksClient from 'jwks-rsa';
import { log } from 'winston';

interface Notification {
    signedPayload: string;
}

interface ResponseBodyV2DecodedPayload {
    notificationType: string;
    subtype?: string;
    notificationUUID: string;
    data: {
        appAppleId: number;
        bundleId: string;
        bundleVersion: string;
        environment: string;
        signedTransactionInfo: string;
        signedRenewalInfo?: string;
    };
}

interface TransactionInfo {
    transactionId: string;
    originalTransactionId: string;
    purchaseDate: string;
    productId: string;
    quantity: number;
    type: string;
    expiresDate?: string;
    isInTrialPeriod: boolean;
    inAppOwnershipType: string;
}

const client = jwksClient({
    jwksUri: 'https://appleid.apple.com/auth/keys'
});

function getKey(header: jwt.JwtHeader, callback: jwt.SigningKeyCallback): void {
    client.getSigningKey(header.kid, function (err, key) {
        const signingKey = key?.getPublicKey();
        callback(err, signingKey);
    });
}

function decodeSignedPayload(signedPayload: string): Promise<ResponseBodyV2DecodedPayload> {
    return new Promise((resolve, reject) => {
        jwt.verify(signedPayload, getKey, { algorithms: ['RS256'] }, (err, decoded) => {
            if (err) {
                return reject(err);
            }
            resolve(decoded as ResponseBodyV2DecodedPayload);
        });
    });
}

function decodeTransactionInfo(signedTransactionInfo: string): Promise<TransactionInfo> {
    return new Promise((resolve, reject) => {
        jwt.verify(signedTransactionInfo, getKey, { algorithms: ['RS256'] }, (err, decoded) => {
            if (err) {
                return reject(err);
            }
            resolve(decoded as TransactionInfo);
        });
    });
}

async function handleRefund(req: Request, res: Response, next: NextFunction) {
    try {
        logger.error('Refund request received path:', req.path);
        logger.error('Refund request received headers:', req.headers);
        logger.error('Refund request received body:', req.body);
        const notification: Notification = req.body;
        logger.error('Refund request received req.body as notification :', notification);

        // Decode the signed payload
        const signedPayload = notification.signedPayload;
        const decodedPayload = await decodeSignedPayload(signedPayload);

        if (decodedPayload.notificationType !== 'REFUND') {
            return next({ message: 'Invalid notification type', code: HttpStatusCode.BadRequest });
        }

        // Decode the signed transaction information
        const signedTransactionInfo = decodedPayload.data.signedTransactionInfo;
        const transactionInfo: TransactionInfo = await decodeTransactionInfo(signedTransactionInfo);

        const transactionId = transactionInfo.transactionId;

        const userVerification = await UserVerification.findOne({ 'verificationResult.receipt.in_app.transaction_id': transactionId }).populate('verificationResult');

        if (!userVerification) {
            return next({ message: 'Transaction not found', code: HttpStatusCode.NotFound });
        }

        const userId = userVerification.userId;
        const amount = userVerification.amount;

        const userBalance = await UserBalance.findOneAndUpdate(
            { userId },
            { $inc: { balance: -amount } },
            { new: true }
        );

        if (!userBalance) {
            return next({ message: 'User balance not found', code: HttpStatusCode.NotFound });
        }

        return res.status(HttpStatusCode.Ok).json({
            message: 'Refund processed and balance updated successfully',
            code: HttpStatusCode.Ok,
            data: userBalance.balance
        });
    } catch (error) {
        console.error(error);
        next({ message: 'Failed to handle refund', code: HttpStatusCode.InternalServerError });
    }
}

export default handleRefund;
