import  { Request, Response, NextFunction} from 'express';
import UserBalance from 'src/models/Balance';
import { HttpStatusCode } from 'axios';
import { logger } from 'src/business/logger';

// 获取用户余额的处理函数
const userBalance = async (req: Request, res: Response, next: NextFunction) => {
    try {
        // // 直接返回401
        // if (1) {
        //     return next({ message: req.t('token_required'), code: HttpStatusCode.Unauthorized });
        // }
        // @ts-ignore
        logger.debug('userBalance: ', req.token);
        // @ts-ignore
        const userId = req.token.userId;
        if (!userId) {
            return next({ message: req.t('user_not_exist'), code: HttpStatusCode.Unauthorized });
        }

        const userBalance = await UserBalance.findOne({ userId }).lean();
        if (!userBalance) {
            return next({ message: req.t('data_not_found'), code: HttpStatusCode.NotFound });
        }

        res.json({ message: 'success', code: HttpStatusCode.Ok, data: userBalance });
    } catch (err) {
        console.error(err);
        next({ message: req.t('internal_server_error'), code: HttpStatusCode.InternalServerError });
    }
};

export default userBalance