import { Request, Response, NextFunction } from 'express';
import { secretKey, tokenExpiresIn } from 'src/config/env';
import jwt from 'jsonwebtoken';
import jwksClient, { SigningKey } from 'jwks-rsa';
import User from 'src/models/User';

// 初始化 jwksClient
const client = jwksClient({
    jwksUri: 'https://appleid.apple.com/auth/keys'
});

// 获取签名密钥的方法
function getKey(header: jwt.JwtHeader, callback: jwt.SigningKeyCallback): void {
    client.getSigningKey(header.kid, function (err, key) {
        const signingKey = key?.getPublicKey();
        callback(err, signingKey);
    });
}

// 验证苹果登录的 idToken
export function verifyAppleIdToken(idToken: string, audience: string): Promise<jwt.JwtPayload> {
    return new Promise((resolve, reject) => {
        jwt.verify(
            idToken,
            getKey,
            {
                algorithms: ['RS256'],
                audience,
                issuer: 'https://appleid.apple.com'
            },
            (err, decoded) => {
                if (err) {
                    return reject(err);
                }
                resolve(decoded as jwt.JwtPayload);
            }
        );
    });
}

// 苹果登录校验逻辑
const appleLogin = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { idToken, appId } = req.body;

        // 验证苹果登录的 idToken
        const appleInfo = await verifyAppleIdToken(idToken, appId);

        // 检查解码后的信息是否有效
        if (!appleInfo || !appleInfo.email) {
            throw new Error('Invalid Apple ID token');
        }

        // 查找邮箱是否存在
        const existingUser = await User.findOne({ email: appleInfo.email });

        if (existingUser) {
            // 检查用户是否已绑定此 Apple 账号
            if (existingUser.externalAccounts && existingUser.externalAccounts.appleInfo && existingUser.externalAccounts.appleInfo.sub === appleInfo.sub) {
                // 更新 isDelete 状态
                await User.updateOne({ email: appleInfo.email }, { isDelete: false });
                // 生成 JWT Token
                const token = jwt.sign({ userId: existingUser._id, email: existingUser.email }, secretKey, { expiresIn: tokenExpiresIn });
                res.status(200).json({
                    message: 'Apple login successful',
                    code: 200,
                    data: { ...existingUser.toObject(), token }
                });
            } else {
                // 存在邮箱但未绑定此 Apple 账号，提示邮箱冲突
                res.status(200).json({
                    message: req.t('email_conflict'),
                    code: 409,
                });
            }
        } else {
            // 创建新用户
            const newUser = new User({
                email: appleInfo.email,
                externalAccounts: {
                    appleInfo
                }
            });
            await newUser.save();
            const token = jwt.sign({ userId: newUser._id, email: newUser.email }, secretKey, { expiresIn: tokenExpiresIn });
            res.status(200).json({
                message: 'Apple login successful',
                code: 200,
                data: { ...newUser.toObject(), token }
            });
        }
    } catch (error: any) {
        console.log(error);
        // 处理错误情况，返回相应的错误响应
        next(error);
    }
};

export  const appleAppSiteAssociation = async (req: Request, res: Response, next: NextFunction) => {
    try {
        res.json({
            "applinks": {
                "apps": [],
                "details": [
                    {
                        "appID": "2XBCJAM843.com.sanva.chatadvisor",
                        "paths": [
                            "/tiktoklogin*",
                            "/tiktokcallbak*"
                        ]
                    }
                ]
            }
        });
    } catch (error: any) {
        console.log(error);
        next(error);
    }
}

export default appleLogin;
