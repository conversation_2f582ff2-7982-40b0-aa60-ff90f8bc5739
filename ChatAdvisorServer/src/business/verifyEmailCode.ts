import { Request, Response, NextFunction } from 'express';
import VerificationCode, { VerificationCodeModel } from 'src/models/VerificationCode';
import User, { IUser } from 'src/models/User';
import { DateTime } from 'luxon';
import { secretKey, tokenExpiresIn } from 'src/config/env';
import env from 'src/config/env';
import jwt from 'jsonwebtoken';
import { logger } from 'src/business/logger';
import { HttpStatusCode } from 'axios';


interface VerifyCodeResponse {
    valid: boolean;
    message?: string;
}

const verifyCode = async (email: string, code: string): Promise<VerifyCodeResponse> => {
    try {
        const verification = await VerificationCode.findOne({ email, code }).lean();
        if (!verification) {
            return { valid: false, message: 'Invalid verification code.' };
        }

        const now = DateTime.now().setZone('Asia/Shanghai');
        const expirationTime = DateTime.fromISO(verification.expiresAt.toISOString()).setZone('Asia/Shanghai');
        if (expirationTime < now) {
            await VerificationCode.deleteOne({ _id: verification._id });
            return { valid: false, message: 'Verification code has expired.' };
        }

        await VerificationCode.deleteOne({ _id: verification._id });
        return { valid: true };
    } catch (error) {
        logger.error(`Error verifying verification code: ${error}`);
        return { valid: false, message: 'An error occurred while verifying verification code.' };
    }
};

export default async function verifyEmailCode(req: Request, res: Response, next: NextFunction) {
    try {
        const { email, code, password } = req.body;
        const { valid, message } = await verifyCode(email, code);

        if (env.isRelease) {
            if (!valid) {
                next({ message: message, code: HttpStatusCode.BadRequest });
                return;
            }
        }

        // const hashedPassword = await bcrypt.hash(password, 10); // 加密密码
        const newUser = new User({ email, password: password, username: email });
        await newUser.save();
        newUser.password = undefined; // 隐藏密码
        const token = jwt.sign({ userId: newUser._id, email: newUser.email }, secretKey, { expiresIn: tokenExpiresIn });

        res.status(HttpStatusCode.Created).json({
            message: '注册成功',
            code: HttpStatusCode.Created,
            data: { ...newUser.toObject(), token }
        });
    } catch (error: any) {
        if (error.code === 11000) {
            error = new Error(req.t('user_exist'));
            error.statusCode = HttpStatusCode.BadRequest; // 设置错误状态码
        } else {
            error.statusCode = HttpStatusCode.InternalServerError; // 内部服务器错误
        }
        next(error); // 将错误传递给错误处理中间件
    }
};
