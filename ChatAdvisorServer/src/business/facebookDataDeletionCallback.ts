import { Request, Response } from 'express';
import User from 'src/models/User';
import DataDeletionRecord from 'src/models/DataDeletionRecord'; // 引入数据删除记录模型
import crypto from 'crypto';

// 生成 6 位确认码
function generateConfirmationCode(): string {
    return crypto.randomBytes(3).toString('hex').toUpperCase();
}

// Facebook 数据删除回调
const facebookDataDeletionCallback = async (req: Request, res: Response) => {
    const { signed_request } = req.body;
    
    if (!signed_request) {
        return res.status(400).send(req.t('invalid_signature'));
    }

    try {
        // 解码 signed_request
        const [encodedSig, payload] = signed_request.split('.');
        const data = JSON.parse(Buffer.from(payload, 'base64').toString('utf8'));

        if (!data || !data.user_id) {
            return res.status(400).send(req.t('invalid_signature'));
        }

        const userId = data.user_id;

        // 在数据库中查找用户并将 isDelete 设置为 true
        const user = await User.findOne({ 'externalAccounts.facebookInfo.id': userId });

        if (user) {
            await User.updateOne(
                { 'externalAccounts.facebookInfo.id': userId },
                { isDelete: true }
            );

            // 生成并保存删除记录
            const confirmationCode = generateConfirmationCode();
            const deletionRecord = new DataDeletionRecord({ userId, confirmationCode });
            await deletionRecord.save();

            // 返回数据删除确认
            res.status(200).json({
                url: `https://api.sanva.tk/deletion?id=${userId}`,
                confirmation_code: confirmationCode
            });
        } else {
            res.status(404).send('User not found');
        }
    } catch (error) {
        console.error('Data deletion error:', error);
        res.status(500).send('Internal server error');
    }
};

export default facebookDataDeletionCallback;
