import { Request, Response, NextFunction } from 'express';
import Pricing, { IPricing } from 'src/models/Pricing';
import { HttpStatusCode } from 'axios';
import { logger } from 'src/business/logger';

export const getPricing = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const pricings = await Pricing.find().lean();
        const lang = req.headers['accept-language']?.split(',')[0].split('-')[0] || 'en'; // 默认为英文
        const formattedPricings = pricings.map((doc: IPricing) => {
            // @ts-ignore
            return {
                id: doc._id,
                modelName: doc.modelName,
                inPrice: doc.inPrice,
                outPrice: doc.outPrice,
                count: doc.count,
                // @ts-ignore
                alias: doc.alias[lang] || doc.alias['en'],
                // @ts-ignore
                intro: doc.intro[lang] || doc.intro['en']
            };
        });

        res.json({
            data: formattedPricings,
            code: HttpStatusCode.Ok
        });
    } catch (error) {
        logger.error(`Error: ${error}`);
        res.status(HttpStatusCode.InternalServerError).json({ code: HttpStatusCode.InternalServerError, message: 'Error accessing the database', error });
    }
};
