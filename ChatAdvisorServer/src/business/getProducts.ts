import { Request, Response, NextFunction } from 'express';
import { Product } from 'src/models/Product';
import { HttpStatusCode } from 'axios';

export const getProducts = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
        const products = await Product.find({ isEnable: true }).lean();
        res.status(HttpStatusCode.Ok).json({ code: HttpStatusCode.Ok, data: products.map((doc) => {
                return {
                    productIdentifier: doc.productIdentifier,
                    amount: doc.amount
                };
            }
          ), message: 'success' });
    } catch (error) {
        next({ code: HttpStatusCode.InternalServerError, message: req.t('internal_server_error') });
    }
};
