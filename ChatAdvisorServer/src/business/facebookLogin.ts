import { Request, Response, NextFunction } from 'express';
import { secretKey, tokenExpiresIn } from 'src/config/env';
import jwt from 'jsonwebtoken';
import User from 'src/models/User';
import axios from 'axios';
import { logger } from './logger';

// 验证 Facebook 登录的 accessToken
export async function verifyFacebookAccessToken(accessToken: string): Promise<any> {
    const response = await axios.get(`https://graph.facebook.com/me?access_token=${accessToken}&fields=id,email`);
    const data = response.data;
    return data;
}

// Facebook 登录校验逻辑
const facebookLogin = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { accessToken } = req.body;

        // 验证 Facebook 登录的 accessToken
        let facebookInfo = await verifyFacebookAccessToken(accessToken);

        // 检查解码后的信息是否有效
        if (!facebookInfo || !facebookInfo.id) {
            throw new Error('Invalid Facebook access token');
        }
        if (!facebookInfo.email) {
            facebookInfo.email = `${facebookInfo.id}@facebook`;
        }

        // 查找邮箱是否存在
        const existingUser = await User.findOne({ email: facebookInfo.email });

        if (existingUser) {
            // 检查用户是否已绑定此 Facebook 账号
            if (existingUser.externalAccounts && existingUser.externalAccounts.facebookInfo && existingUser.externalAccounts.facebookInfo.id === facebookInfo.id) {
                // 更新 isDelete 状态
                await User.updateOne({ email: facebookInfo.email }, { isDelete: false });
                // 生成 JWT Token
                const token = jwt.sign({ userId: existingUser._id, email: existingUser.email }, secretKey, { expiresIn: tokenExpiresIn });
                res.status(200).json({
                    message: 'Facebook login successful',
                    code: 200,
                    data: { ...existingUser.toObject(), token }
                });
            } else {
                
                // 存在邮箱但未绑定此 Facebook 账号，提示邮箱冲突
                res.status(200).json({
                    message: req.t('email_conflict'),
                    code: 409,
                });
            }
        } else {
            // 创建新用户
            const newUser = new User({
                email: facebookInfo.email,
                externalAccounts: {
                    facebookInfo
                }
            });
            await newUser.save();
            const token = jwt.sign({ userId: newUser._id, email: newUser.email }, secretKey, { expiresIn: tokenExpiresIn });
            res.status(200).json({
                message: 'Facebook login successful',
                code: 200,
                data: { ...newUser.toObject(), token }
            });
        }
    } catch (error: any) {
        console.log(error);
        // 处理错误情况，返回相应的错误响应
        next(error);
    }
};

export default facebookLogin;
