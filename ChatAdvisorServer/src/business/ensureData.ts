import Pricing from 'src/models/Pricing';
import { Question } from 'src/models/Question';
import { Product } from 'src/models/Product';
import { Config } from 'src/models/Config';
import pricingData from 'src/config/Pricing';
import questionsData from 'src/config/Questions';
import config from 'src/config/Config';
import { logger } from 'src/business/logger';

export async function ensureData() {
    await Pricing.deleteMany({}).exec();

    for (const item of pricingData) {
        // 重新添加
        const newItem = new Pricing(item);
        await newItem.save();
        logger.debug(`增加模型: ${item.alias.zh} ${item.modelName}`);
    }

    

    const products = [
        { productIdentifier: 'com.sanva.advisor.1', amount: 99, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.2', amount: 199, isEnable: true, nation: 'en' },
        // 3填错了，用31.。。
        { productIdentifier: 'com.sanva.advisor.31', amount: 299, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.4', amount: 399, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.5', amount: 499, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.6', amount: 599, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.7', amount: 699, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.8', amount: 799, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.9', amount: 899, isEnable: true, nation: 'en' }
    ];

    for (const item of questionsData) {
        const exists = await Question.findOne({ sketch: item.sketch }).exec();
        if (!exists) {
            const newItem = new Question(item);
            await newItem.save();
        } else {
            // update price
            await Question.updateOne(
                { sketch: item.sketch },
                {
                    content: item.content,
                    question: item.question
                }
            ).exec();
        }
    }

    for (const item of products) {
        const exists = await Product.findOne({ productIdentifier: item.productIdentifier }).exec();
        if (!exists) {
            const newItem = new Product(item);
            await newItem.save();
        } else {
            // update price
            await Product.updateOne(
                { productIdentifier: item.productIdentifier },
                {
                    amount: item.amount,
                    isEnable: item.isEnable
                }
            ).exec();
        }
    }

    const exists = await Config.findOne({ appVersion: config.appVersion }).exec();

    if (!exists) {
        await config.save();
    } else {
        // 除了id以外都更新
        await Config.updateOne(
            { appVersion: config.appVersion },
            {
                privacyPolicy: config.privacyPolicy,
                termsOfService: config.termsOfService,
                supportEmail: config.supportEmail,
                featureFlags: config.featureFlags,
                mainSolgan: config.mainSolgan,
                registerSolgan: config.registerSolgan,
                emailLoginSolgan: config.emailLoginSolgan,
                rechargeMessages: config.rechargeMessages,
                hideMessage: config.hideMessage,
                rechargeDescription: config.rechargeDescription,
                promotLocal: config.promotLocal,
                promotCloud: config.promotCloud,
                compressRate: config.compressRate,
            }
        ).exec();
    }
}
