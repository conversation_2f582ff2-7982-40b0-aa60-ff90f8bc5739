# MongoDB副本集配置指南

## 问题描述

当您遇到以下错误时：
```
Transaction numbers are only allowed on a replica set member or mongos
```

这表明您的MongoDB实例不支持事务，因为事务只能在副本集(replica set)或分片集群(mongos)上运行。

## 解决方案

我们已经为您配置了MongoDB副本集支持，包括：

### 1. 配置文件修改

- **连接字符串更新**：在 `src/config/env.ts` 中添加了 `?replicaSet=rs0` 参数
- **副本集配置**：`mongod.conf` 中已配置副本集名称为 `rs0`
- **初始化代码**：启用了 `database.ts` 中的副本集初始化逻辑

### 2. 自动化脚本

创建了 `scripts/setup-replica-set.js` 脚本来自动设置副本集。

## 使用方法

### 快速设置（推荐）

```bash
# 进入项目目录
cd ChatAdvisorServer

# 运行快速设置脚本（推荐）
npm run quick-setup

# 或者运行完整设置脚本
npm run setup-replica

# 启动应用
npm run dev
```

**注意**：如果遇到 `mongo: command not found` 错误，请确保安装了 MongoDB Shell (mongosh)：

```bash
# macOS
brew install mongosh

# 其他系统请访问：https://docs.mongodb.com/mongodb-shell/install/
```

### 手动设置

如果自动脚本失败，可以手动执行以下步骤：

#### macOS用户：

```bash
# 1. 启动MongoDB服务
brew services start mongodb-community

# 2. 连接到MongoDB并初始化副本集
mongosh --eval "rs.initiate()"

# 3. 检查副本集状态
mongosh --eval "rs.status()"
```

#### Linux用户：

```bash
# 1. 启动MongoDB服务
sudo systemctl start mongod

# 2. 连接到MongoDB并初始化副本集
mongo --eval "rs.initiate()"

# 3. 检查副本集状态
mongo --eval "rs.status()"
```

## 验证配置

### 1. 检查副本集状态

```bash
mongosh --eval "rs.status()"
```

成功的输出应该包含：
- `"set" : "rs0"`
- `"myState" : 1` (PRIMARY状态)

### 2. 测试事务功能

启动应用后，尝试发送聊天消息。如果不再出现事务错误，说明配置成功。

## 故障排除

### 常见问题

1. **MongoDB服务未启动**
   ```bash
   # macOS
   brew services start mongodb-community
   
   # Linux
   sudo systemctl start mongod
   ```

2. **副本集已存在错误**
   - 这是正常的，表示副本集已经初始化过了

3. **连接超时**
   - 确保MongoDB服务正在运行
   - 检查防火墙设置
   - 确认端口27017可访问

### 重置副本集

如果需要重置副本集配置：

```bash
# 停止MongoDB
brew services stop mongodb-community  # macOS
# 或
sudo systemctl stop mongod  # Linux

# 删除数据目录中的副本集配置
rm -rf /usr/local/var/mongodb/local.*

# 重新启动并初始化
npm run setup-replica
```

## 技术说明

### 为什么需要副本集？

MongoDB事务功能需要副本集或分片集群环境，因为：
1. 事务需要oplog来记录操作
2. 需要多数派确认机制
3. 单实例无法提供这些功能

### 配置详情

- **副本集名称**：`rs0`
- **成员数量**：1个（单节点副本集）
- **端口**：27017
- **数据目录**：`/usr/local/var/mongodb`

### 性能影响

单节点副本集相比单实例：
- 轻微的性能开销（约5-10%）
- 增加了oplog存储需求
- 提供了事务支持和更好的数据一致性

## 后续优化

在生产环境中，建议：
1. 配置多节点副本集（3个节点）
2. 设置适当的读写关注级别
3. 配置监控和告警
4. 定期备份oplog
