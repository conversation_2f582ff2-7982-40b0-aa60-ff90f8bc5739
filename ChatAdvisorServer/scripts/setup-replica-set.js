#!/usr/bin/env node

const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// 检查当前操作系统
const isMacOS = process.platform === 'darwin';

async function checkMongoStatus() {
    try {
        const command = isMacOS ? 'brew services list | grep mongodb' : 'systemctl status mongod';
        const { stdout } = await execAsync(command);
        console.log('MongoDB服务状态:', stdout);
        return true;
    } catch (error) {
        console.error('检查MongoDB状态失败:', error.message);
        return false;
    }
}

async function startMongoDB() {
    try {
        const command = isMacOS ? 'brew services start mongodb-community' : 'sudo systemctl start mongod';
        await execAsync(command);
        console.log('MongoDB服务启动成功');
        
        // 等待MongoDB完全启动
        await new Promise(resolve => setTimeout(resolve, 3000));
        return true;
    } catch (error) {
        console.error('启动MongoDB失败:', error.message);
        return false;
    }
}

async function initiateReplicaSet() {
    try {
        const command = isMacOS ? 'mongosh --eval "rs.initiate()"' : 'mongo --eval "rs.initiate()"';
        const { stdout, stderr } = await execAsync(command);
        
        if (stderr && stderr.includes('already initialized')) {
            console.log('副本集已经初始化');
            return true;
        }
        
        console.log('副本集初始化成功:', stdout);
        return true;
    } catch (error) {
        if (error.message.includes('already initialized')) {
            console.log('副本集已经初始化');
            return true;
        }
        console.error('初始化副本集失败:', error.message);
        return false;
    }
}

async function checkReplicaSetStatus() {
    try {
        const command = isMacOS ? 'mongosh --eval "rs.status()"' : 'mongo --eval "rs.status()"';
        const { stdout } = await execAsync(command);
        console.log('副本集状态:', stdout);
        return true;
    } catch (error) {
        console.error('检查副本集状态失败:', error.message);
        return false;
    }
}

async function main() {
    console.log('开始设置MongoDB副本集...');
    
    // 1. 检查MongoDB状态
    console.log('\n1. 检查MongoDB服务状态...');
    const isRunning = await checkMongoStatus();
    
    if (!isRunning) {
        console.log('\n2. 启动MongoDB服务...');
        const started = await startMongoDB();
        if (!started) {
            console.error('无法启动MongoDB服务，请手动检查');
            process.exit(1);
        }
    }
    
    // 3. 初始化副本集
    console.log('\n3. 初始化副本集...');
    const initiated = await initiateReplicaSet();
    if (!initiated) {
        console.error('副本集初始化失败');
        process.exit(1);
    }
    
    // 4. 等待副本集稳定
    console.log('\n4. 等待副本集稳定...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 5. 检查副本集状态
    console.log('\n5. 检查副本集状态...');
    await checkReplicaSetStatus();
    
    console.log('\n✅ MongoDB副本集设置完成！');
    console.log('现在可以使用事务功能了。');
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main };
