#!/usr/bin/env node

const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// 检查当前操作系统
const isMacOS = process.platform === 'darwin';

async function quickSetup() {
    console.log('🚀 快速设置MongoDB副本集...\n');
    
    try {
        // 1. 检查mongosh是否可用
        console.log('1. 检查mongosh命令...');
        try {
            await execAsync('mongosh --version');
            console.log('✅ mongosh命令可用');
        } catch (error) {
            console.log('❌ mongosh命令不可用，请安装MongoDB Shell');
            console.log('安装方法：');
            if (isMacOS) {
                console.log('  brew install mongosh');
            } else {
                console.log('  请访问 https://docs.mongodb.com/mongodb-shell/install/');
            }
            process.exit(1);
        }
        
        // 2. 启动MongoDB服务
        console.log('\n2. 启动MongoDB服务...');
        const startCommand = isMacOS ? 'brew services start mongodb-community' : 'sudo systemctl start mongod';
        try {
            await execAsync(startCommand);
            console.log('✅ MongoDB服务启动成功');
        } catch (error) {
            console.log('⚠️  MongoDB服务可能已经在运行');
        }
        
        // 3. 等待服务启动
        console.log('\n3. 等待MongoDB服务完全启动...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 4. 初始化副本集
        console.log('\n4. 初始化副本集...');
        try {
            const { stdout, stderr } = await execAsync('mongosh --eval "rs.initiate()"');
            
            if (stderr && (stderr.includes('already initialized') || stderr.includes('AlreadyInitialized'))) {
                console.log('✅ 副本集已经初始化');
            } else if (stdout && (stdout.includes('ok') || stdout.includes('already initialized'))) {
                console.log('✅ 副本集初始化成功');
            } else {
                console.log('✅ 副本集配置完成');
            }
        } catch (error) {
            if (error.message.includes('already initialized') || error.message.includes('AlreadyInitialized')) {
                console.log('✅ 副本集已经初始化');
            } else {
                throw error;
            }
        }
        
        // 5. 等待副本集稳定
        console.log('\n5. 等待副本集稳定...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 6. 验证副本集状态
        console.log('\n6. 验证副本集状态...');
        try {
            const { stdout } = await execAsync('mongosh --eval "rs.status()" --quiet');
            
            if (stdout.includes('"set" : "rs0"') && stdout.includes('"myState" : 1')) {
                console.log('✅ 副本集状态正常 - PRIMARY节点运行中');
            } else if (stdout.includes('rs0')) {
                console.log('⚠️  副本集已配置，可能需要更多时间稳定');
            } else {
                console.log('❌ 副本集状态检查失败');
                console.log('输出:', stdout);
            }
        } catch (error) {
            console.log('⚠️  副本集状态检查失败，但这可能是正常的');
        }
        
        console.log('\n🎉 MongoDB副本集设置完成！');
        console.log('\n现在可以启动应用程序：');
        console.log('  npm run dev');
        console.log('\n如果仍然遇到事务错误，请等待几分钟让副本集完全稳定。');
        
    } catch (error) {
        console.error('\n❌ 设置过程中出现错误:', error.message);
        console.log('\n请尝试手动执行以下命令：');
        if (isMacOS) {
            console.log('  brew services start mongodb-community');
        } else {
            console.log('  sudo systemctl start mongod');
        }
        console.log('  mongosh --eval "rs.initiate()"');
        process.exit(1);
    }
}

if (require.main === module) {
    quickSetup().catch(console.error);
}

module.exports = { quickSetup };
