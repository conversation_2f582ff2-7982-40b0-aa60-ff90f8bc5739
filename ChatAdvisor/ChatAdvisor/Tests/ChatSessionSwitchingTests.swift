//
//  ChatSessionSwitchingTests.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/02.
//

import XCTest
import Combine
@testable import ChatAdvisor

class ChatSessionSwitchingTests: XCTestCase {
    var contentViewModel: ContentViewModel!
    var chatListViewModel: ChatListViewModel!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        contentViewModel = ContentViewModel()
        chatListViewModel = ChatListViewModel()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        cancellables.forEach { $0.cancel() }
        cancellables = nil
        contentViewModel = nil
        chatListViewModel = nil
        super.tearDown()
    }
    
    // MARK: - 状态同步测试
    
    func testSelectChatAtomically_ShouldUpdateSelectedChatID() async throws {
        // Given
        let testChat = Chat(id: "test-chat-1", messages: [], title: "测试会话")
        
        // When
        try await contentViewModel.selectChatAtomically(chat: testChat, chatListViewModel: chatListViewModel)
        
        // Then
        XCTAssertEqual(contentViewModel.selectedChatID, testChat.id)
        XCTAssertNotNil(contentViewModel.currentChatViewModel)
        XCTAssertEqual(contentViewModel.currentChatViewModel?.currentChat.id, testChat.id)
    }
    
    func testSelectChatAtomically_WithInvalidChatID_ShouldThrowError() async {
        // Given
        let invalidChat = Chat(id: "", messages: [], title: "无效会话")
        
        // When & Then
        do {
            try await contentViewModel.selectChatAtomically(chat: invalidChat, chatListViewModel: chatListViewModel)
            XCTFail("应该抛出错误")
        } catch ChatError.invalidChatID {
            // 预期的错误
        } catch {
            XCTFail("抛出了意外的错误: \(error)")
        }
    }
    
    func testSelectChatAtomically_ShouldMaintainStateConsistency() async throws {
        // Given
        let chat1 = Chat(id: "chat-1", messages: [], title: "会话1")
        let chat2 = Chat(id: "chat-2", messages: [], title: "会话2")
        
        // When
        try await contentViewModel.selectChatAtomically(chat: chat1, chatListViewModel: chatListViewModel)
        try await contentViewModel.selectChatAtomically(chat: chat2, chatListViewModel: chatListViewModel)
        
        // Then
        XCTAssertEqual(contentViewModel.selectedChatID, chat2.id)
        XCTAssertEqual(contentViewModel.currentChatViewModel?.currentChat.id, chat2.id)
        XCTAssertEqual(contentViewModel.chatViewModels.count, 2)
    }
    
    // MARK: - ChatViewModel缓存测试
    
    func testChatViewModelCaching_ShouldReuseExistingViewModel() async throws {
        // Given
        let testChat = Chat(id: "test-chat", messages: [], title: "测试会话")
        
        // When
        try await contentViewModel.selectChatAtomically(chat: testChat, chatListViewModel: chatListViewModel)
        let firstViewModel = contentViewModel.currentChatViewModel
        
        try await contentViewModel.selectChatAtomically(chat: testChat, chatListViewModel: chatListViewModel)
        let secondViewModel = contentViewModel.currentChatViewModel
        
        // Then
        XCTAssertTrue(firstViewModel === secondViewModel, "应该重用相同的ChatViewModel实例")
    }
    
    func testChatViewModelCaching_ShouldLimitCacheSize() async throws {
        // Given
        let maxCachedViewModels = 5
        var chats: [Chat] = []
        for i in 1...7 {
            chats.append(Chat(id: "chat-\(i)", messages: [], title: "会话\(i)"))
        }
        
        // When
        for chat in chats {
            try await contentViewModel.selectChatAtomically(chat: chat, chatListViewModel: chatListViewModel)
        }
        
        // Then
        XCTAssertLessThanOrEqual(contentViewModel.chatViewModels.count, maxCachedViewModels)
    }
    
    // MARK: - 错误处理测试
    
    func testErrorHandling_ShouldSetErrorMessage() async {
        // Given
        let invalidChat = Chat(id: "", messages: [], title: "无效会话")
        
        // When
        do {
            try await contentViewModel.selectChatAtomically(chat: invalidChat, chatListViewModel: chatListViewModel)
        } catch {
            // 忽略错误，我们只关心错误消息是否被设置
        }
        
        // Then
        XCTAssertNotNil(contentViewModel.chatLoadingError)
        XCTAssertTrue(contentViewModel.chatLoadingError?.contains("会话选择失败") == true)
    }
    
    // MARK: - 数据库状态测试
    
    func testDatabaseReadyCheck_ShouldWaitForDatabase() async {
        // Given
        let databaseManager = AdvisorDatabaseManager.shared
        
        // When
        let isReady = await databaseManager.waitForDatabaseReady()
        
        // Then
        XCTAssertTrue(isReady || !databaseManager.isDatabaseReady, "数据库状态检查应该正确")
    }
    
    // MARK: - 性能测试
    
    func testPerformance_FastChatSwitching() {
        measure {
            let expectation = XCTestExpectation(description: "快速会话切换")
            
            Task {
                let chats = (1...10).map { Chat(id: "chat-\($0)", messages: [], title: "会话\($0)") }
                
                for chat in chats {
                    try? await contentViewModel.selectChatAtomically(chat: chat, chatListViewModel: chatListViewModel)
                }
                
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 5.0)
        }
    }
    
    // MARK: - 集成测试

    func testIntegration_CompleteSessionSwitchingFlow() async throws {
        // Given
        let chat1 = Chat(id: "integration-chat-1", messages: [
            ChatMessage(id: "msg-1", chatId: "integration-chat-1", role: .user, content: "Hello")
        ], title: "集成测试会话1")

        let chat2 = Chat(id: "integration-chat-2", messages: [
            ChatMessage(id: "msg-2", chatId: "integration-chat-2", role: .user, content: "World")
        ], title: "集成测试会话2")

        // When
        try await contentViewModel.selectChatAtomically(chat: chat1, chatListViewModel: chatListViewModel)
        let firstSelection = contentViewModel.selectedChatID

        try await contentViewModel.selectChatAtomically(chat: chat2, chatListViewModel: chatListViewModel)
        let secondSelection = contentViewModel.selectedChatID

        // Then
        XCTAssertEqual(firstSelection, chat1.id)
        XCTAssertEqual(secondSelection, chat2.id)
        XCTAssertEqual(contentViewModel.chatViewModels.count, 2)
        XCTAssertNil(contentViewModel.chatLoadingError)
    }

    // MARK: - ChatSessionManager集成测试

    func testChatSessionManager_Integration() async throws {
        // Given
        let sessionManager = ChatSessionManager.shared
        let testChat = Chat(id: "session-manager-test", messages: [], title: "SessionManager测试")

        // When
        try await sessionManager.switchToSession(chatId: testChat.id, chatListViewModel: chatListViewModel)

        // Then
        XCTAssertNotNil(sessionManager.currentSession)
        XCTAssertEqual(sessionManager.currentSession?.id, testChat.id)
    }

    // MARK: - 性能监控测试

    func testPerformanceMonitor_Integration() {
        // Given
        let monitor = PerformanceMonitor.shared
        monitor.startMonitoring()

        // When
        monitor.startOperation("test_operation", type: .sessionSwitch)
        Thread.sleep(forTimeInterval: 0.1) // 模拟操作耗时
        monitor.endOperation("test_operation", type: .sessionSwitch, success: true)

        // Then
        let report = monitor.generateReport(for: 60)
        XCTAssertGreaterThan(report.totalOperations, 0)
        XCTAssertEqual(report.successRate, 1.0)

        monitor.stopMonitoring()
    }

    // MARK: - 增强加载视图测试

    func testEnhancedLoadingView_States() {
        // Given & When & Then
        let loadingView = EnhancedLoadingView(state: .loading("测试加载"))
        XCTAssertNotNil(loadingView)

        let skeletonView = EnhancedLoadingView(state: .skeleton)
        XCTAssertNotNil(skeletonView)

        let errorView = EnhancedLoadingView(state: .error("测试错误")) {
            // 重试回调
        }
        XCTAssertNotNil(errorView)

        let emptyView = EnhancedLoadingView(state: .empty("测试空状态"))
        XCTAssertNotNil(emptyView)
    }
}

// MARK: - 测试辅助方法

extension ChatSessionSwitchingTests {
    
    /// 创建测试用的Chat对象
    private func createTestChat(id: String, messageCount: Int = 0) -> Chat {
        var messages: [ChatMessage] = []
        for i in 1...messageCount {
            messages.append(ChatMessage(
                id: "msg-\(i)",
                chatId: id,
                role: i % 2 == 1 ? .user : .assistant,
                content: "测试消息 \(i)"
            ))
        }
        return Chat(id: id, messages: messages, title: "测试会话 \(id)")
    }
    
    /// 等待异步操作完成
    private func waitForAsyncOperation(timeout: TimeInterval = 1.0) async {
        try? await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
    }
}
