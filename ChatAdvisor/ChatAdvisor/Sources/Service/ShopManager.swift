//
//  ShopManager.swift
//  JunShi
//
//  Created by zweiteng on 2024/5/18.
//

import Combine
import Foundation
import Localize_Swift
import OSLog
import StoreKit

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "StoreManager")

class ShopManager: NSObject, ObservableObject, SKProductsRequestDelegate, SKPaymentTransactionObserver {
    static let shared = ShopManager()
    @Published var backendProducts: [Product] = []
    @Published var products: [SKProduct] = []
    @Published var selectProduct: SKProduct?
    @Published var purchaseState: PurchaseState = .idle
    @Published var isLoading = false
    private var cancellables: Set<AnyCancellable> = []
    private var productsRequest: SKProductsRequest?

    override init() {
        super.init()
        SKPaymentQueue.default().add(self)
    }

    deinit {
        SKPaymentQueue.default().remove(self)
        cancellables.forEach { $0.cancel() }
    }

    func fetchProducts(products: [Product]) {
        isLoading = true
        let productIdentifiers = Set(products.map(\.productIdentifier))
        productsRequest = SKProductsRequest(productIdentifiers: productIdentifiers)
        productsRequest?.delegate = self
        productsRequest?.start()
    }

    func getProducts() {
        DispatchQueue.main.async {
            self.isLoading = true
            self.purchaseState = .idle
        }
        NetworkService.shared.requestMulti(PurchaseTarget.getProducts) { [weak self] (result: Result<NetworkResponse<[Product]>, NetworkError>) in
            guard let self else { return }
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                switch result {
                case let .success(response):
                    if response.isSuccess {
                        backendProducts = response.data ?? []
                        fetchProducts(products: backendProducts)
                    }
                case let .failure(error):
                    logger.error("error: \(error)")
                    purchaseState = .failed(message: "获取商品失败".localized())
                }
                isLoading = false
            }
        }
    }

    func buyProduct(_ product: SKProduct) {
        guard SKPaymentQueue.canMakePayments() else {
            purchaseState = .failed(message: "当前设备没有开启应用内购买功能".localized())
            return
        }

        let payment = SKPayment(product: product)
        SKPaymentQueue.default().add(payment)
    }

    func productsRequest(_: SKProductsRequest, didReceive response: SKProductsResponse) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isLoading = false
            products = response.products
        }
    }

    func paymentQueue(_: SKPaymentQueue, updatedTransactions transactions: [SKPaymentTransaction]) {
        for transaction in transactions {
            switch transaction.transactionState {
            case .purchased:
                FirebaseManager.shared.logPurchase(productId: selectProduct?.productIdentifier ?? "异常id", price: selectProduct?.localizedPrice ?? "异常价格", currency: selectProduct?.localizedTitle ?? "异常currency")
                complete(transaction: transaction)
            case .failed:
                fail(transaction: transaction)
            case .restored:
                restore(transaction: transaction)
            case .purchasing, .deferred:
                break
            @unknown default:
                break
            }
        }
    }

    private func complete(transaction: SKPaymentTransaction) {
//        self.isLoading = false
//        purchaseState = .success

        // 获取交易收据数据
        if let receiptData = getReceiptData(transaction: transaction), let product = selectProduct {
            // 假设你有获取用户ID和交易金额的方法
            let userId = AccountManager.shared.currentUser?.userId ?? ""
            let amount = selectProduct?.price.floatValue ?? 0
            verifyReceipt(userId: userId, productIdentifier: product.productIdentifier, receiptData: receiptData)
        }

        SKPaymentQueue.default().finishTransaction(transaction)
    }

    private func getReceiptData(transaction _: SKPaymentTransaction) -> String? {
        // 获取收据数据的实际实现，根据需求进行调整
        guard let receiptURL = Bundle.main.appStoreReceiptURL,
              let receiptData = try? Data(contentsOf: receiptURL).base64EncodedString()
        else {
            return nil
        }
        return receiptData
    }

    private func fail(transaction: SKPaymentTransaction) {
        isLoading = false
        if let error = transaction.error as? SKError {
            let errorCode = error.code
            let errorUserInfo = error.userInfo

            switch errorCode {
            case .paymentCancelled:
                purchaseState = .failed(isCancel: true, message: "用户取消购买")
            default:
                if let errorMessage = errorUserInfo[NSLocalizedDescriptionKey] as? String {
                    purchaseState = .failed(message: errorMessage)
                } else {
                    purchaseState = .failed(message: "未知错误")
                }
            }
        } else {
            purchaseState = .failed(message: "未知错误")
        }
        SKPaymentQueue.default().finishTransaction(transaction)
    }

    private func restore(transaction: SKPaymentTransaction) {
        purchaseState = .restored
        SKPaymentQueue.default().finishTransaction(transaction)
    }

    func paymentQueueRestoreCompletedTransactionsFinished(_: SKPaymentQueue) {
        purchaseState = .restored
    }

    func paymentQueue(_: SKPaymentQueue, restoreCompletedTransactionsFailedWithError error: Error) {
        purchaseState = .failed(message: error.localizedDescription)
    }

    // todo
    private func verifyReceipt(userId: String, productIdentifier: String, receiptData: String?) {
        guard let receiptData else {
            purchaseState = .failed(message: "错误的收据")
            return
        }
        NetworkService.shared.requestMulti(PurchaseTarget.verifyPurchase(userId: userId, productIdentifier: productIdentifier, receipt: receiptData)) { [weak self] (result: Result<NetworkResponse<Double>, NetworkError>) in
            guard let self else { return }
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                switch result {
                case let .success(response):
                    if response.isSuccess {
                        AccountManager.shared.currentUser?.balance = response.data ?? 0
                        AccountManager.shared.hasPurchase = true
                        purchaseState = .success
                    } else {
                        purchaseState = .failed(message: response.message ?? "购买失败")
                    }
                case let .failure(error):
                    logger.error("error: \(error)")
                    purchaseState = .failed(message: "购买失败,如果您已支付成功,请尝试重启应用")
                }
            }
        }
    }
}

enum PurchaseState {
    case idle
    case success
    case failed(isCancel: Bool = false, message: String)
    case restored
}
