//
//  AccountManager.swift
//  JunShi
//
//  Created by md on 2024/4/19.
//

import Foundation
import KeychainSwift
import OSLog
import SwiftUI

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "AuthManager")

extension Notification.Name {
    static let tokenExpired = Notification.Name("kNotificationTokenExpired")
    static let logout = Notification.Name("kNotificationLogOut")
    static let loginSuccess = Notification.Name("kNotificationLoginSuccess")
    static let databaseSetupCompleted = Notification.Name("kNotificationDatabaseSetupCompleted")
    static let databaseSetupFailed = Notification.Name("kNotificationDatabaseSetupFailed")
}

class AccountManager: ObservableObject {
    static let shared = AccountManager()
    private let keychain = KeychainSwift()
    @Published var isLoggedIn: Bool = false {
        didSet {
            guard isLoggedIn != oldValue else { return }
            needLoggedIn = !isLoggedIn
        }
    }

    var hasPurchase: Bool {
        get {
            if let emailMD5 = AccountManager.shared.currentUser?.email.md5 {
                let hasPurchaseKey = emailMD5 + "hasPurchase"
                let keychain = KeychainSwift()
                return keychain.getBool(hasPurchaseKey) ?? false
            }
            return false
        }
        set {
            if let emailMD5 = AccountManager.shared.currentUser?.email.md5 {
                let hasPurchaseKey = emailMD5 + "hasPurchase"
                let keychain = KeychainSwift()
                keychain.set(newValue, forKey: hasPurchaseKey)
            }
        }
    }

    @Published var needLoggedIn: Bool = false
    @Published var isLoading = false
    @Published var showToast = false
    @Published var isRequestError = false
    @Published var toastMessage = ""

    var resumeBlock: (() -> Void)? = nil

    init() {
        withAnimation {
            isLoggedIn = currentUser != nil
        }

        // 监听token过期
        NotificationCenter.default.addObserver(forName: .tokenExpired, object: nil, queue: nil) { _ in
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                self.logout()
            }
            
//            self.currentUser = nil
//            DispatchQueue.main.async {
//                withAnimation {
//                    self.needLoggedIn = true
//                }
//            }
//            ChatConfigDatabaseManager.shared.closeDatabase()
//            AdvisorDatabaseManager.shared.closeDatabase()
        }
    }

    var currentUser: User? {
        get {
            UserSettings.user
        }
        set {
            if newValue == UserSettings.user {
                return
            }
            Preferences.hasLoginedBefore.value = true
            if let token = newValue?.token, token != UserSettings.user?.token {
                Preferences.tokenLastUpdate.value = Date()
            }
            UserSettings.user = newValue
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                withAnimation {
                    self.isLoggedIn = newValue != nil
                }
            }
        }
    }

    func login(username: String, password: String) {
        AuthViewModel().login(credentials: UserCredentials(email: username, password: password))
    }

    func afterLogin() {
        DispatchQueue.main.async {
            self.resumeBlock?()
            self.resumeBlock = nil
        }
        BootManager.shared.getConfig()
        BootManager.shared.getPricing()
        ShopManager.shared.getProducts()

        // 设置数据库，数据库设置完成后会发送通知
        AdvisorDatabaseManager.shared.setupDatabase()
        ChatConfigDatabaseManager.shared.setupDatabase()

        // 注意：不再立即发送loginSuccess通知，而是等待数据库设置完成
        // 数据库设置完成后会发送databaseSetupCompleted通知
        //        getBalance()
    }

    func refreshTokenIfNeed() {
        // 离上次更新超过3天则刷新token
//        guard Preferences.tokenLastUpdate.value.timeIntervalSinceNow < -3 * 24 * 60 * 60 else {
//            return
//        }
        NetworkService.shared.requestMulti(AuthTarget.refreshToken) { (result: Result<NetworkResponse<String>, NetworkError>) in
            switch result {
            case let .success(res):
                if res.isSuccess, let token = res.data {
                    // todo fix me 会触发afterLogin,内部判断相同return
                    AccountManager.shared.currentUser?.token = token
                }
            case let .failure(error):
                logger.error("refresh token error: \(error.localizedDescription)")
            }
        }
    }

    func getBalance() {
        guard currentUser != nil else {
            return
        }
        NetworkService.shared.requestMulti(PricingTarget.balance) { (result: Result<NetworkResponse<Balance>, NetworkError>) in
//            guard let self = self else { return }
            switch result {
            case let .success(res):
                if res.isSuccess, let balance = res.data?.balance {
                    // todo fix me 会触发afterLogin,内部判断相同return
                    AccountManager.shared.currentUser?.balance = balance
                }
            case let .failure(error):
                logger.error("get user Balance error: \(error.localizedDescription)")
            }
        }
    }

    func logout() {
        NotificationCenter.default.post(name: .logout, object: nil)
        UserSettings.user = nil
//        AuthManager.shared.currentUser = nil
        ChatConfigDatabaseManager.shared.closeDatabase()
        AdvisorDatabaseManager.shared.closeDatabase()
        withAnimation {
            needLoggedIn = true
            isLoggedIn = false
        }
    }

    func deleteAccount(password: String?) {
        isLoading = true
        NetworkService.shared.requestMulti(AuthTarget.deleteAccount(password: password)) { [weak self] (result: Result<NetworkResponse<String>, NetworkError>) in
            guard let self else { return }
            switch result {
            case let .success(res):
                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    if res.isSuccess {
                        FirebaseManager.shared.logDeleteUser(userId: currentUser?.userId ?? "")
                        logout()
                        AdvisorDatabaseManager.shared.cleanDataBase()
                        AdvisorDatabaseManager.shared.setupDatabase()
                    } else {
                        failedToast(res.message ?? "删除失败".localized())
                    }
                    isLoading = false
                }
            case let .failure(error):
                logger.error("refresh token error: \(error.localizedDescription)")
            }
        }
    }
}

extension AccountManager {
    func failedToast(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isRequestError = true
            toastMessage = message
            showToast = true
        }
    }

    func successToast(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isRequestError = false
            toastMessage = message
            showToast = true
        }
    }
}
