//
//  ChatListPerformanceMonitor.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ChatListPerformanceMonitor")

/// 性能指标
struct PerformanceMetrics {
    let operationName: String
    let duration: TimeInterval
    let timestamp: Date
    let metadata: [String: Any]
    
    init(operationName: String, duration: TimeInterval, metadata: [String: Any] = [:]) {
        self.operationName = operationName
        self.duration = duration
        self.timestamp = Date()
        self.metadata = metadata
    }
}

/// 性能统计
struct PerformanceStats {
    let operationName: String
    let totalCount: Int
    let averageDuration: TimeInterval
    let minDuration: TimeInterval
    let maxDuration: TimeInterval
    let lastExecutionTime: Date
    
    var isPerformanceGood: Bool {
        return averageDuration < 1.0 // 1秒以内认为性能良好
    }
    
    var performanceLevel: PerformanceLevel {
        switch averageDuration {
        case 0..<0.1:
            return .excellent
        case 0.1..<0.5:
            return .good
        case 0.5..<1.0:
            return .acceptable
        case 1.0..<3.0:
            return .poor
        default:
            return .critical
        }
    }
}

/// 性能等级
enum PerformanceLevel: String, CaseIterable {
    case excellent = "优秀"
    case good = "良好"
    case acceptable = "可接受"
    case poor = "较差"
    case critical = "严重"
    
    var color: String {
        switch self {
        case .excellent:
            return "🟢"
        case .good:
            return "🟡"
        case .acceptable:
            return "🟠"
        case .poor:
            return "🔴"
        case .critical:
            return "🚨"
        }
    }
}

/// 聊天列表性能监控器
class ChatListPerformanceMonitor {
    static let shared = ChatListPerformanceMonitor()
    
    // MARK: - Private Properties
    private var metrics: [PerformanceMetrics] = []
    private var operationStats: [String: PerformanceStats] = [:]
    private let queue = DispatchQueue(label: "com.sanva.chatadvisor.performance", qos: .utility)
    private let maxMetricsCount = 1000 // 最大保存的指标数量
    
    // 性能阈值
    private let warningThreshold: TimeInterval = 1.0
    private let criticalThreshold: TimeInterval = 3.0
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// 记录操作性能
    func recordOperation(_ operationName: String, duration: TimeInterval, metadata: [String: Any] = [:]) {
        let metric = PerformanceMetrics(
            operationName: operationName,
            duration: duration,
            metadata: metadata
        )
        
        queue.async { [weak self] in
            self?.addMetric(metric)
            self?.updateStats(for: operationName, duration: duration)
            self?.checkPerformanceThresholds(metric)
        }
    }
    
    /// 测量操作性能
    func measureOperation<T>(_ operationName: String, metadata: [String: Any] = [:], operation: () throws -> T) rethrows -> T {
        let startTime = Date()
        let result = try operation()
        let duration = Date().timeIntervalSince(startTime)
        
        recordOperation(operationName, duration: duration, metadata: metadata)
        return result
    }
    
    /// 异步测量操作性能
    func measureAsyncOperation<T>(_ operationName: String, metadata: [String: Any] = [:], operation: () async throws -> T) async rethrows -> T {
        let startTime = Date()
        let result = try await operation()
        let duration = Date().timeIntervalSince(startTime)
        
        recordOperation(operationName, duration: duration, metadata: metadata)
        return result
    }
    
    /// 获取操作统计
    func getStats(for operationName: String) -> PerformanceStats? {
        return queue.sync {
            return operationStats[operationName]
        }
    }
    
    /// 获取所有统计
    func getAllStats() -> [PerformanceStats] {
        return queue.sync {
            return Array(operationStats.values).sorted { $0.averageDuration > $1.averageDuration }
        }
    }
    
    /// 获取性能报告
    func getPerformanceReport() -> String {
        let stats = getAllStats()
        
        var report = "📊 聊天列表性能报告\n"
        report += "=" * 50 + "\n\n"
        
        if stats.isEmpty {
            report += "暂无性能数据\n"
            return report
        }
        
        // 总体统计
        let totalOperations = stats.reduce(0) { $0 + $1.totalCount }
        let averagePerformance = stats.reduce(0.0) { $0 + $1.averageDuration } / Double(stats.count)
        
        report += "📈 总体统计:\n"
        report += "  • 总操作数: \(totalOperations)\n"
        report += "  • 平均耗时: \(String(format: "%.3f", averagePerformance))s\n"
        report += "  • 监控操作: \(stats.count) 种\n\n"
        
        // 详细统计
        report += "📋 详细统计:\n"
        for stat in stats {
            report += "  \(stat.performanceLevel.color) \(stat.operationName):\n"
            report += "    - 执行次数: \(stat.totalCount)\n"
            report += "    - 平均耗时: \(String(format: "%.3f", stat.averageDuration))s\n"
            report += "    - 最快/最慢: \(String(format: "%.3f", stat.minDuration))s / \(String(format: "%.3f", stat.maxDuration))s\n"
            report += "    - 性能等级: \(stat.performanceLevel.rawValue)\n\n"
        }
        
        // 性能建议
        let poorPerformanceOps = stats.filter { $0.performanceLevel == .poor || $0.performanceLevel == .critical }
        if !poorPerformanceOps.isEmpty {
            report += "⚠️ 性能建议:\n"
            for op in poorPerformanceOps {
                report += "  • \(op.operationName): 性能\(op.performanceLevel.rawValue)，建议优化\n"
            }
        }
        
        return report
    }
    
    /// 清理旧数据
    func cleanup() {
        queue.async { [weak self] in
            guard let self = self else { return }
            
            // 只保留最近的指标
            if self.metrics.count > self.maxMetricsCount {
                let removeCount = self.metrics.count - self.maxMetricsCount
                self.metrics.removeFirst(removeCount)
            }
            
            logger.info("性能监控数据清理完成")
        }
    }
    
    /// 重置所有数据
    func reset() {
        queue.async { [weak self] in
            self?.metrics.removeAll()
            self?.operationStats.removeAll()
            logger.info("性能监控数据已重置")
        }
    }
    
    /// 导出性能数据
    func exportData() -> [String: Any] {
        return queue.sync {
            return [
                "metrics": metrics.map { metric in
                    [
                        "operationName": metric.operationName,
                        "duration": metric.duration,
                        "timestamp": metric.timestamp.timeIntervalSince1970,
                        "metadata": metric.metadata
                    ]
                },
                "stats": operationStats.mapValues { stat in
                    [
                        "operationName": stat.operationName,
                        "totalCount": stat.totalCount,
                        "averageDuration": stat.averageDuration,
                        "minDuration": stat.minDuration,
                        "maxDuration": stat.maxDuration,
                        "lastExecutionTime": stat.lastExecutionTime.timeIntervalSince1970,
                        "performanceLevel": stat.performanceLevel.rawValue
                    ]
                }
            ]
        }
    }
    
    // MARK: - Private Methods
    
    private func addMetric(_ metric: PerformanceMetrics) {
        metrics.append(metric)
        
        // 自动清理
        if metrics.count > maxMetricsCount {
            metrics.removeFirst()
        }
    }
    
    private func updateStats(for operationName: String, duration: TimeInterval) {
        if let existingStats = operationStats[operationName] {
            let newCount = existingStats.totalCount + 1
            let newAverage = (existingStats.averageDuration * Double(existingStats.totalCount) + duration) / Double(newCount)
            let newMin = min(existingStats.minDuration, duration)
            let newMax = max(existingStats.maxDuration, duration)
            
            operationStats[operationName] = PerformanceStats(
                operationName: operationName,
                totalCount: newCount,
                averageDuration: newAverage,
                minDuration: newMin,
                maxDuration: newMax,
                lastExecutionTime: Date()
            )
        } else {
            operationStats[operationName] = PerformanceStats(
                operationName: operationName,
                totalCount: 1,
                averageDuration: duration,
                minDuration: duration,
                maxDuration: duration,
                lastExecutionTime: Date()
            )
        }
    }
    
    private func checkPerformanceThresholds(_ metric: PerformanceMetrics) {
        if metric.duration > criticalThreshold {
            logger.error("🚨 严重性能问题: \(metric.operationName) 耗时 \(metric.duration)s")
        } else if metric.duration > warningThreshold {
            logger.warning("⚠️ 性能警告: \(metric.operationName) 耗时 \(metric.duration)s")
        }
    }
}

// MARK: - 便利扩展
extension ChatListPerformanceMonitor {
    
    /// 记录数据库操作性能
    func recordDatabaseOperation(_ operation: String, duration: TimeInterval, recordCount: Int = 0) {
        let metadata: [String: Any] = recordCount > 0 ? ["recordCount": recordCount] : [:]
        recordOperation("Database.\(operation)", duration: duration, metadata: metadata)
    }
    
    /// 记录UI操作性能
    func recordUIOperation(_ operation: String, duration: TimeInterval) {
        recordOperation("UI.\(operation)", duration: duration)
    }
    
    /// 记录网络操作性能
    func recordNetworkOperation(_ operation: String, duration: TimeInterval, dataSize: Int = 0) {
        let metadata: [String: Any] = dataSize > 0 ? ["dataSize": dataSize] : [:]
        recordOperation("Network.\(operation)", duration: duration, metadata: metadata)
    }
}

// MARK: - String扩展
private extension String {
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
}
