//
//  BootsManager.swift
//  JunShi
//
//  Created by md on 2024/5/16.
//

import Foundation
import Moya
import Network
import OSLog
import StoreKit

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "BootConfigManager")

class BootManager: NSObject, ObservableObject {
    static let shared = BootManager()
    @Published var isLoading = false
    @Published var isChina: Bool = false
    @Published var config: Configs = .default
    @Published var questions: [Question] = []
    @Published var allChatModels: [ChatsModel] = []

    override init() {
        super.init()
        isChina = SKPaymentQueue.default().storefront?.countryCode == "CHN"
        SKPaymentQueue.default().add(self)
    }

    func getConfig() {
        executeOnMainThread {
            self.isLoading = true
        }
        NetworkService.shared.requestMulti(ConfigTarget.getConfig) { [weak self] (result: Result<NetworkResponse<Configs>, NetworkError>) in
            guard let self else { return }
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                switch result {
                case let .success(response):
                    if response.isSuccess {
                        config = response.data ?? Configs.default
                        // 处理版本控制信息
                        handleVersionControl(from: config)
                    } else if config == nil {
                        config = Configs.default
                    }
                case let .failure(error):
                    logger.error("error: \(error)")
                    // 如果网络请求失败且config为nil，设置默认配置
                    if config == nil {
                        config = Configs.default
                    }
                }
                isLoading = false
            }
        }
    }

    /// 处理版本控制信息
    /// - Parameter config: 配置信息
    private func handleVersionControl(from config: Configs) {
        guard let versionControl = config.versionControl,
              versionControl.versionCheckEnabled else {
            logger.info("版本检测已禁用或版本控制信息为空")
            return
        }

        // 如果需要更新，通知版本更新管理器
        if versionControl.needUpdate {
            logger.info("检测到需要版本更新：\(versionControl.latestVersion)")
            VersionUpdateManager.shared.showUpdatePrompt(with: versionControl)
        } else {
            logger.info("当前版本已是最新版本")
        }
    }

    /// 主动检查版本更新
    /// - Parameter force: 是否强制检查
    func checkForUpdates(force: Bool = false) {
        logger.info("主动检查版本更新...")
        VersionUpdateManager.shared.checkForUpdates(force: force)
    }

    func getPricing() {
        NetworkService.shared.requestMulti(PricingTarget.getPricing) { [weak self] (result: Result<NetworkResponse<[ChatsModel]>, NetworkError>) in
            guard let self else { return }
            switch result {
            case let .success(response):
                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    if response.isSuccess {
                        allChatModels = response.data ?? []
                        ChatViewModel.allModels = response.data ?? []
                    } else {
                        allChatModels = []
                    }
                }
            case let .failure(error):
                logger.error("getPricing failed: \(error.localizedDescription)")
            }
        }
    }
}

extension BootManager: SKPaymentTransactionObserver {
    func paymentQueue(_: SKPaymentQueue, updatedTransactions _: [SKPaymentTransaction]) {}

    func paymentQueueDidChangeStorefront(_ queue: SKPaymentQueue) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isChina = queue.storefront?.countryCode == "CHN"
        }
    }

    func executeOnMainThread(_ block: @escaping () -> Void) {
        if Thread.isMainThread {
            block()
        } else {
            DispatchQueue.main.async {
                block()
            }
        }
    }
}
