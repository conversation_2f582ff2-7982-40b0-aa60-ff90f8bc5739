//
//  DataSyncManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "DataSyncManager")

/// 数据同步管理器 - 负责Repository和现有数据库管理器之间的数据同步
class DataSyncManager {
    static let shared = DataSyncManager()
    
    private let chatRepository: ChatRepositoryProtocol
    private let messageRepository: MessageRepositoryProtocol
    private let legacyDatabaseManager = AdvisorDatabaseManager.shared
    
    private init() {
        // 初始化Repository
        let database = AdvisorDatabaseManager.shared.database!
        let databaseQueue = DispatchQueue(label: "com.sanva.chatadvisor.datasync", qos: .background)
        
        let chatCacheManager = try! CacheManager<Chat>(name: "chats_sync")
        let messageCacheManager = try! CacheManager<ChatMessage>(name: "messages_sync")
        
        self.chatRepository = ChatRepository(
            database: database,
            databaseQueue: databaseQueue,
            cacheManager: chatCacheManager
        )
        
        self.messageRepository = MessageRepository(
            database: database,
            databaseQueue: databaseQueue,
            cacheManager: messageCacheManager
        )
    }
    
    // MARK: - 聊天同步方法
    
    /// 同步聊天到数据库（兼容现有接口）
    func syncChatToDatabase(_ chat: Chat) async throws {
        do {
            // 检查聊天是否已存在
            if let existingChat = try await chatRepository.fetchChat(id: chat.id) {
                // 更新现有聊天
                try await chatRepository.updateChat(chat)
                logger.debug("聊天更新成功: \(chat.id)")
            } else {
                // 保存新聊天
                try await chatRepository.saveChat(chat)
                logger.debug("聊天保存成功: \(chat.id)")
            }
            
            // 同步消息
            try await syncMessagesToDatabase(chat.messages)
            
        } catch {
            logger.error("聊天同步失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 同步消息到数据库
    func syncMessageToDatabase(_ message: ChatMessage) async throws {
        do {
            // 检查消息是否已存在
            if let existingMessage = try await messageRepository.fetchMessage(id: message.id) {
                // 更新现有消息
                try await messageRepository.updateMessage(message)
                logger.debug("消息更新成功: \(message.id)")
            } else {
                // 保存新消息
                try await messageRepository.saveMessage(message)
                logger.debug("消息保存成功: \(message.id)")
            }
        } catch {
            logger.error("消息同步失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 批量同步消息到数据库
    func syncMessagesToDatabase(_ messages: [ChatMessage]) async throws {
        guard !messages.isEmpty else { return }
        
        do {
            // 分批处理消息以避免性能问题
            let batchSize = 50
            for i in stride(from: 0, to: messages.count, by: batchSize) {
                let endIndex = min(i + batchSize, messages.count)
                let batch = Array(messages[i..<endIndex])
                
                // 检查哪些消息需要更新，哪些需要插入
                var messagesToUpdate: [ChatMessage] = []
                var messagesToInsert: [ChatMessage] = []
                
                for message in batch {
                    if let _ = try await messageRepository.fetchMessage(id: message.id) {
                        messagesToUpdate.append(message)
                    } else {
                        messagesToInsert.append(message)
                    }
                }
                
                // 批量操作
                if !messagesToInsert.isEmpty {
                    try await messageRepository.batchSaveMessages(messagesToInsert)
                }
                
                if !messagesToUpdate.isEmpty {
                    try await messageRepository.batchUpdateMessages(messagesToUpdate)
                }
            }
            
            logger.info("批量消息同步成功: \(messages.count) 条消息")
        } catch {
            logger.error("批量消息同步失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    // MARK: - 数据迁移方法
    
    /// 从现有数据库迁移数据到新的Repository系统
    func migrateExistingData() async throws {
        logger.info("开始数据迁移...")
        
        do {
            // 迁移聊天数据
            let existingChats = await legacyDatabaseManager.fetchAllChats()
            for chat in existingChats {
                try await syncChatToDatabase(chat)
            }
            
            logger.info("数据迁移完成: \(existingChats.count) 个聊天")
        } catch {
            logger.error("数据迁移失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    // MARK: - 冲突解决方法
    
    /// 处理数据冲突
    func handleConflictResolution(_ localData: Any, _ remoteData: Any) async throws {
        // 简单的冲突解决策略：使用最新的时间戳
        if let localChat = localData as? Chat,
           let remoteChat = remoteData as? Chat {
            
            let resolvedChat = localChat.createdTime > remoteChat.createdTime ? localChat : remoteChat
            try await syncChatToDatabase(resolvedChat)
            
        } else if let localMessage = localData as? ChatMessage,
                  let remoteMessage = remoteData as? ChatMessage {
            
            let resolvedMessage = localMessage.createdTime > remoteMessage.createdTime ? localMessage : remoteMessage
            try await syncMessageToDatabase(resolvedMessage)
        }
    }
    
    // MARK: - 兼容性方法
    
    /// 提供与现有AdvisorDatabaseManager兼容的接口
    func updateChat(_ chat: Chat) {
        Task {
            try await syncChatToDatabase(chat)
        }
    }
    
    func updateMessage(_ message: ChatMessage) {
        Task {
            try await syncMessageToDatabase(message)
        }
    }
    
    func fetchChat(id: String) async -> Chat? {
        do {
            return try await chatRepository.fetchChat(id: id)
        } catch {
            logger.error("获取聊天失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    func fetchChatSync(id: String) -> Chat? {
        return chatRepository.fetchChatSync(id: id)
    }
    
    func fetchMessages(chatId: String, limit: Int = 20, before: [ChatMessage] = []) async -> [ChatMessage] {
        do {
            return try await messageRepository.fetchMessages(chatId: chatId, limit: limit, before: before)
        } catch {
            logger.error("获取消息失败: \(error.localizedDescription)")
            return []
        }
    }
    
    // MARK: - 数据验证方法
    
    /// 验证数据一致性
    func validateDataConsistency() async throws {
        logger.info("开始数据一致性验证...")
        
        // 验证聊天数据
        let legacyChats = await legacyDatabaseManager.fetchAllChats()
        for chat in legacyChats {
            if let repoChat = try await chatRepository.fetchChat(id: chat.id) {
                if chat.title != repoChat.title || chat.createdTime != repoChat.createdTime {
                    logger.warning("聊天数据不一致: \(chat.id)")
                    // 可以选择修复不一致的数据
                    try await chatRepository.updateChat(chat)
                }
            }
        }
        
        logger.info("数据一致性验证完成")
    }
    
    // MARK: - 性能监控方法
    
    /// 获取同步性能统计
    func getSyncPerformanceStats() -> (chatCount: Int, messageCount: Int, cacheHitRate: Double) {
        // 这里可以添加性能统计逻辑
        return (chatCount: 0, messageCount: 0, cacheHitRate: 0.0)
    }
}
