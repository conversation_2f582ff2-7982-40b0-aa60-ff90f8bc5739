//
//  ChatListDataSource.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import Combine
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ChatListDataSource")

/// 聊天列表数据变更事件
enum ChatListDataEvent {
    case dataLoaded([Chat])
    case chatAdded(Chat)
    case chatUpdated(Chat)
    case chatRemoved(String)
    case dataCleared
    case loadMoreCompleted([Chat])
    case searchCompleted([Chat])
}

/// 聊天列表单一数据源 - 负责管理所有聊天数据的真实来源
@MainActor
class ChatListDataSource: ObservableObject {
    
    // MARK: - Published Properties
    @Published private(set) var chats: [Chat] = []
    @Published private(set) var groupedChats: [Date: [Chat]] = [:]
    @Published private(set) var hasMoreData: Bool = true
    @Published private(set) var totalCount: Int = 0
    
    // MARK: - Private Properties
    private let chatRepository: ChatRepositoryProtocol
    private let messageRepository: MessageRepositoryProtocol
    private let isArchived: Bool
    private let pageSize: Int = 20
    
    // 数据变更事件发布器
    private let dataEventSubject = PassthroughSubject<ChatListDataEvent, Never>()
    var dataEventPublisher: AnyPublisher<ChatListDataEvent, Never> {
        dataEventSubject.eraseToAnyPublisher()
    }
    
    // 性能监控
    private var lastLoadTime: Date = Date()
    private var loadCount: Int = 0
    private let performanceMonitor = ChatListPerformanceMonitor.shared
    
    // MARK: - Initialization
    init(chatRepository: ChatRepositoryProtocol, 
         messageRepository: MessageRepositoryProtocol,
         isArchived: Bool = false) {
        self.chatRepository = chatRepository
        self.messageRepository = messageRepository
        self.isArchived = isArchived
    }
    
    // MARK: - Public Methods
    
    /// 初始化加载数据
    func loadInitialData() async throws {
        try await performanceMonitor.measureAsyncOperation("ChatList.LoadInitialData") {
            logger.info("开始初始化加载聊天列表")

            let loadedChats = try await chatRepository.fetchChats(
                limit: pageSize,
                offset: 0,
                isArchived: isArchived
            )

            // 更新数据
            chats = loadedChats
            updateGroupedChats()
            hasMoreData = loadedChats.count == pageSize
            totalCount = try await chatRepository.getChatCount(isArchived: isArchived)

            // 发布事件
            dataEventSubject.send(.dataLoaded(loadedChats))

            // 性能监控
            loadCount += 1
            logger.info("初始化加载完成: \(loadedChats.count) 条数据")
        }
    }
    
    /// 加载更多数据
    func loadMoreData() async throws {
        guard hasMoreData else {
            logger.debug("没有更多数据可加载")
            return
        }
        
        let startTime = Date()
        logger.info("开始加载更多数据，当前数量: \(self.chats.count)")
        
        do {
            let moreChats = try await chatRepository.fetchChats(
                limit: pageSize,
                offset: chats.count,
                isArchived: isArchived
            )
            
            // 更新数据
            chats.append(contentsOf: moreChats)
            updateGroupedChats()
            hasMoreData = moreChats.count == pageSize
            
            // 发布事件
            dataEventSubject.send(.loadMoreCompleted(moreChats))
            
            // 性能监控
            let loadTime = Date().timeIntervalSince(startTime)
            logger.info("加载更多完成: \(moreChats.count) 条新数据, 耗时: \(loadTime * 1000)ms")
            
        } catch {
            logger.error("加载更多数据失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 搜索聊天
    func searchChats(keyword: String) async throws {
        let startTime = Date()
        logger.info("开始搜索聊天: \(keyword)")
        
        do {
            let searchResults = try await chatRepository.searchChats(
                keyword: keyword,
                isArchived: isArchived
            )
            
            // 更新数据（搜索时替换当前数据）
            chats = searchResults
            updateGroupedChats()
            hasMoreData = false // 搜索结果不支持分页
            
            // 发布事件
            dataEventSubject.send(.searchCompleted(searchResults))
            
            // 性能监控
            let searchTime = Date().timeIntervalSince(startTime)
            logger.info("搜索完成: \(searchResults.count) 条结果, 耗时: \(searchTime * 1000)ms")
            
        } catch {
            logger.error("搜索失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 添加新聊天
    func addChat(_ chat: Chat) async throws {
        logger.info("添加新聊天: \(chat.id)")
        
        // 检查是否已存在
        guard !chats.contains(where: { $0.id == chat.id }) else {
            logger.warning("聊天已存在，跳过添加: \(chat.id)")
            return
        }
        
        // 只有包含用户消息的聊天才添加到列表
        let hasUserMessages = chat.messages.contains { $0.role == .user }
        guard hasUserMessages else {
            logger.debug("聊天无用户消息，跳过添加: \(chat.id)")
            return
        }
        
        do {
            // 保存到数据库
            try await chatRepository.saveChat(chat)
            
            // 更新内存数据（插入到开头，因为是最新的）
            chats.insert(chat, at: 0)
            updateGroupedChats()
            totalCount += 1
            
            // 发布事件
            dataEventSubject.send(.chatAdded(chat))
            
            logger.debug("聊天添加成功: \(chat.id)")
            
        } catch {
            logger.error("添加聊天失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 更新聊天
    func updateChat(_ chat: Chat) async throws {
        logger.info("更新聊天: \(chat.id)")
        
        guard let index = chats.firstIndex(where: { $0.id == chat.id }) else {
            // 如果不存在，尝试添加
            try await addChat(chat)
            return
        }
        
        do {
            // 更新数据库
            try await chatRepository.updateChat(chat)
            
            // 更新内存数据
            chats[index] = chat
            updateGroupedChats()
            
            // 发布事件
            dataEventSubject.send(.chatUpdated(chat))
            
            logger.debug("聊天更新成功: \(chat.id)")
            
        } catch {
            logger.error("更新聊天失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 删除聊天
    func removeChat(id: String) async throws {
        logger.info("删除聊天: \(id)")
        
        guard let index = chats.firstIndex(where: { $0.id == id }) else {
            logger.warning("聊天不存在，跳过删除: \(id)")
            return
        }
        
        do {
            // 从数据库删除
            try await chatRepository.deleteChat(id: id)
            
            // 从内存删除
            chats.remove(at: index)
            updateGroupedChats()
            totalCount = max(0, totalCount - 1)
            
            // 发布事件
            dataEventSubject.send(.chatRemoved(id))
            
            logger.debug("聊天删除成功: \(id)")
            
        } catch {
            logger.error("删除聊天失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 批量删除聊天
    func removeChats(ids: [String]) async throws {
        logger.info("批量删除聊天: \(ids.count) 个")
        
        do {
            // 批量从数据库删除
            try await chatRepository.batchDeleteChats(ids: ids)
            
            // 从内存删除
            chats.removeAll { ids.contains($0.id) }
            updateGroupedChats()
            totalCount = max(0, totalCount - ids.count)
            
            // 发布事件
            for id in ids {
                dataEventSubject.send(.chatRemoved(id))
            }
            
            logger.debug("批量删除聊天成功: \(ids.count) 个")
            
        } catch {
            logger.error("批量删除聊天失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 清空所有数据
    func clearAllData() {
        chats.removeAll()
        groupedChats.removeAll()
        hasMoreData = true
        totalCount = 0
        
        // 发布事件
        dataEventSubject.send(.dataCleared)
        
        logger.info("聊天列表数据已清空")
    }
    
    /// 刷新数据（重新加载）
    func refreshData() async throws {
        clearAllData()
        try await loadInitialData()
    }
    
    /// 获取指定聊天
    func getChat(id: String) -> Chat? {
        return chats.first { $0.id == id }
    }
    
    /// 获取聊天索引
    func getChatIndex(id: String) -> Int? {
        return chats.firstIndex { $0.id == id }
    }
    
    /// 获取性能统计
    func getPerformanceStats() -> (loadCount: Int, averageLoadTime: TimeInterval, totalChats: Int) {
        return (
            loadCount: loadCount,
            averageLoadTime: Date().timeIntervalSince(lastLoadTime),
            totalChats: chats.count
        )
    }
    
    // MARK: - Private Methods
    
    private func updateGroupedChats() {
        groupedChats = Dictionary(grouping: chats) { chat in
            Calendar.current.startOfDay(for: Date(timeIntervalSince1970: TimeInterval(chat.createdTime)))
        }
    }
}
