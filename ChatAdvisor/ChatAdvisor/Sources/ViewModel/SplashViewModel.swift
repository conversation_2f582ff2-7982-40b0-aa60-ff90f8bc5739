//
//  SplashViewModel.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/02.
//

import Foundation
import SwiftUI
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "SplashViewModel")

/// 开屏初始化视图模型
@MainActor
class SplashViewModel: ObservableObject {
    @Published var progress: Double = 0.0
    @Published var statusText: String = "正在启动..."
    @Published var logoScale: CGFloat = 1.0
    @Published var textOpacity: Double = 0.0
    
    private var initializationSteps: [InitializationStep] = []
    private var currentStepIndex = 0
    
    init() {
        setupInitializationSteps()
        startAnimations()
    }
    
    /// 设置初始化步骤
    private func setupInitializationSteps() {
        initializationSteps = [
            InitializationStep(
                name: "初始化数据库连接",
                action: { try? await self.initializeDatabase() }
            ),
            InitializationStep(
                name: "加载用户配置",
                action: { try? await self.loadUserConfiguration() }
            ),
            InitializationStep(
                name: "获取服务器配置",
                action: { try? await self.loadServerConfiguration() }
            ),
            InitializationStep(
                name: "预加载会话数据",
                action: { try? await self.preloadSessionData() }
            ),
            InitializationStep(
                name: "准备用户界面",
                action: { try? await self.prepareUserInterface() }
            ),
            InitializationStep(
                name: "完成初始化",
                action: { try? await self.finalizeInitialization() }
            )
        ]
    }
    
    /// 启动动画
    private func startAnimations() {
        logoScale = 1.2
        textOpacity = 1.0
    }
    
    /// 开始初始化过程
    func startInitialization(completion: @escaping (Bool) -> Void) {
        Task {
            var success = true
            
            for (index, step) in initializationSteps.enumerated() {
                currentStepIndex = index
                
                // 更新状态文本
                statusText = step.name
                
                // 更新进度
                let newProgress = Double(index) / Double(initializationSteps.count)
                withAnimation(.easeInOut(duration: 0.3)) {
                    progress = newProgress
                }
                
                do {
                    // 执行初始化步骤
                    try await step.action()
                    
                    // 每个步骤之间稍微延迟，让用户看到进度
                    try await Task.sleep(nanoseconds: 300_000_000) // 300ms
                    
                } catch {
                    logger.error("初始化步骤失败: \(step.name), 错误: \(error)")
                    success = false
                    break
                }
            }
            
            // 完成进度
            withAnimation(.easeInOut(duration: 0.5)) {
                progress = 1.0
                statusText = success ? "初始化完成" : "初始化失败"
            }
            
            completion(success)
        }
    }
    
    // MARK: - 初始化步骤实现
    
    /// 初始化数据库连接
    private func initializeDatabase() async throws {
        // 确保数据库管理器已初始化
        _ = AdvisorDatabaseManager.shared.database
        
        // 等待数据库准备完成
        let isDatabaseReady = await AdvisorDatabaseManager.shared.waitForDatabaseReady()
        guard isDatabaseReady else {
            throw InitializationError.databaseInitializationFailed
        }
        
        logger.info("数据库初始化完成")
    }
    
    /// 加载用户配置
    private func loadUserConfiguration() async throws {
        // 预加载用户偏好设置
        _ = Preferences.hasLaunchedBefore.value

        // 如果用户已登录，预加载账户信息
        if AccountManager.shared.isLoggedIn {
            // 这里可以预加载一些用户相关的配置
        }

        logger.info("用户配置加载完成")
    }

    /// 加载服务器配置
    private func loadServerConfiguration() async throws {
        // 获取服务器配置，包括版本控制信息
        await withCheckedContinuation { continuation in
            BootManager.shared.getConfig()

            // 等待配置加载完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                continuation.resume()
            }
        }

        logger.info("服务器配置加载完成")
    }
    
    /// 预加载会话数据
    private func preloadSessionData() async throws {
        // 预查询最后一个聊天ID
        let lastChatId = AdvisorDatabaseManager.shared.getLastChatId()
        
        if let lastChatId = lastChatId {
            // 预加载最后一个会话的数据
            _ = await AdvisorDatabaseManager.shared.fetchChat(id: lastChatId)
            logger.info("预加载会话数据完成: \(lastChatId)")
        } else {
            logger.info("没有找到历史会话")
        }
    }
    
    /// 准备用户界面
    private func prepareUserInterface() async throws {
        // 预加载一些UI相关的资源
        // 这里可以预加载图片、字体等资源
        
        // 初始化性能监控
        PerformanceMonitor.shared.startMonitoring()
        
        logger.info("用户界面准备完成")
    }
    
    /// 完成初始化
    private func finalizeInitialization() async throws {
        // 最后的清理和验证工作
        logger.info("所有初始化步骤完成")
    }
}

// MARK: - 支持类型

/// 初始化步骤
struct InitializationStep {
    let name: String
    let action: () async throws -> Void
}

/// 初始化错误
enum InitializationError: LocalizedError {
    case databaseInitializationFailed
    case userConfigurationLoadFailed
    case sessionDataPreloadFailed
    case userInterfacePreparationFailed
    
    var errorDescription: String? {
        switch self {
        case .databaseInitializationFailed:
            return "数据库初始化失败"
        case .userConfigurationLoadFailed:
            return "用户配置加载失败"
        case .sessionDataPreloadFailed:
            return "会话数据预加载失败"
        case .userInterfacePreparationFailed:
            return "用户界面准备失败"
        }
    }
}
