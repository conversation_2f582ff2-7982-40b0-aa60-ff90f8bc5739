//
//  Chats.swift
//
//
//  Created by zwt on 2024/4/9.
//

import Foundation
import WCDBSwift

enum MessageType: String, Codable, ColumnCodable {
    case text
    case image
    case audio
    case video
    case file
    case location
    case contact
    case system

    init?(with value: WCDBSwift.Value) {
        self.init(rawValue: value.stringValue)
    }

    func archivedValue() -> WCDBSwift.Value {
        WCDBSwift.Value(rawValue)
    }

    // 定义存储到数据库中的数据类型
    static var columnType: ColumnType {
        .text
    }

    // codable
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        let rawValue = try container.decode(String.self)
        self = MessageType(rawValue: rawValue) ?? .text
    }
}

/// 消息状态枚举
enum MessageStatus {
    case sending     // 发送中
    case sent        // 已发送
    case receiving   // 接收中
    case completed   // 已完成
    case failed      // 发送失败
    case cancelled   // 已取消
}

enum Role: String, Codable, ColumnCodable {
    case user
    case assistant
    case system

    init?(with value: WCDBSwift.Value) {
        self.init(rawValue: value.stringValue)
    }

    func archivedValue() -> WCDBSwift.Value {
        WCDBSwift.Value(rawValue)
    }

    // 定义存储到数据库中的数据类型
    static var columnType: ColumnType {
        .text
    }

    // codable
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        let rawValue = try container.decode(String.self)
        self = Role(rawValue: rawValue) ?? .user
    }
}

enum PostMessageType: Codable {
    case chatMessage(ChatMessage)
    case postMessage(PostMessage)
    // 编码
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        switch self {
        case let .chatMessage(message):
            try container.encode(message)
        case let .postMessage(message):
            try container.encode(message)
        }
    }

    // 解码
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        if let chatMessage = try? container.decode(ChatMessage.self) {
            self = .chatMessage(chatMessage)
            return
        }
        if let postMessage = try? container.decode(PostMessage.self) {
            self = .postMessage(postMessage)
            return
        }
        throw DecodingError.dataCorruptedError(in: container, debugDescription: "Unable to decode as ChatMessage or PostMessage")
    }

    private enum CodingKeys: String, CodingKey {
        case type
        case content
    }
}

struct PostMessageBody: Codable {
    let messages: [PostMessageType]

    // 编码 - 确保编码为包含 messages 字段的对象
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(messages, forKey: .messages)
    }

    enum CodingKeys: String, CodingKey {
        case messages
    }
}

struct PostMessage: Codable {
    let role: Role
    let content: [Content]
    let total_token_cost: Int

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(role, forKey: .role)
        try container.encode(content, forKey: .content)
        try container.encode(total_token_cost, forKey: .total_token_cost)
    }

    enum CodingKeys: String, CodingKey {
        case role
        case content
        case total_token_cost
    }

    struct Content: Codable {
        let type: String
        let text: String?
        let imageUrl: ImageUrl?
        let width: Double
        let height: Double
        let token_cost: Int

        func encode(to encoder: Encoder) throws {
            var container = encoder.container(keyedBy: CodingKeys.self)
            try container.encode(type, forKey: .type)
            if type == "text", text != nil {
                try container.encode(text, forKey: .text)
            }
            if type == "image_url", imageUrl != nil {
                try container.encode(imageUrl, forKey: .imageUrl)
            }
            try container.encode(width, forKey: .width)
            try container.encode(height, forKey: .height)
            try container.encode(token_cost, forKey: .token_cost)
        }

        enum CodingKeys: String, CodingKey {
            case type
            case text
            case imageUrl = "image_url"
            case width
            case height
            case token_cost
        }

        struct ImageUrl: Codable {
            let url: String
            let detail: String

            enum CodingKeys: String, CodingKey {
                case url
                case detail
            }
        }
    }
}

struct ChatMessage: Identifiable, Codable, Equatable, TableCodable {
    let viewId: String = UUID().uuidString
    var id: String
    let chatId: String
    var role: Role
    var content: String
    var createdTime: Int64
    var isComplete: Bool = false
    var messageType: MessageType = .text
    var assetURL: URL?
    var finishReason: FinishReason? = nil

    // 新增：消息状态
    var status: MessageStatus {
        if role == .user {
            return isComplete ? .sent : .sending
        } else {
            if let finishReason = finishReason {
                switch finishReason {
                case .stop:
                    return .completed
                case .error:
                    return .failed
                case .noBalance:
                    return .failed
                case .length:
                    return .completed
                case .contentFilter:
                    return .failed
                case .none:
                    return .receiving
                }
            }
            return isComplete ? .completed : .receiving
        }
    }

    init(id: String, chatId: String, role: Role, content: String, createdTime: Int64 = Int64(Date().timeIntervalSince1970), isComplete: Bool = false, messageType: MessageType = .text, assetURL: URL? = nil, finishReason: FinishReason? = nil) {
        self.id = id
        self.chatId = chatId
        self.role = role
        self.content = content
        self.createdTime = createdTime
        self.isComplete = isComplete
        self.messageType = messageType
        self.assetURL = assetURL
        self.finishReason = finishReason
    }

    func copyWith(content: String) -> ChatMessage {
        ChatMessage(id: id, chatId: chatId, role: role, content: content, createdTime: createdTime, isComplete: isComplete, finishReason: finishReason)
    }

    mutating func append(content newContent: String) {
        if !newContent.isEmpty {
            content += newContent
        }
    }

    enum CodingKeys: String, CodingTableKey {
        typealias Root = ChatMessage
        static let objectRelationalMapping = TableBinding(CodingKeys.self) {
            BindColumnConstraint(createdTime, orderBy: .ascending)
        }

        case id
        case chatId
        case role
        case content
        case createdTime
        case isComplete
        case finishReason
    }

    static var customColumnMapping: [CodingKeys: String]? {
        [
            .id: "id",
            .chatId: "chatId",
            .role: "role",
            .content: "content",
            .createdTime: "createdTime",
            .isComplete: "isComplete",
            .finishReason: "finishReason"
        ]
    }

    // codable
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        chatId = try container.decode(String.self, forKey: .chatId)
        role = try container.decode(Role.self, forKey: .role)
        content = try container.decode(String.self, forKey: .content)
        createdTime = try container.decode(Int64.self, forKey: .createdTime)
        isComplete = try container.decode(Bool.self, forKey: .isComplete)
        finishReason = try container.decodeIfPresent(FinishReason.self, forKey: .finishReason)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(chatId, forKey: .chatId)
        try container.encode(role, forKey: .role)
        try container.encode(content, forKey: .content)
        try container.encode(createdTime, forKey: .createdTime)
        try container.encode(isComplete, forKey: .isComplete)
        try container.encodeIfPresent(finishReason, forKey: .finishReason)
    }
}

struct Chat: Identifiable, Codable, Equatable, TableCodable, Hashable, ExpressionConvertible {
    func asExpression() -> WCDBSwift.Expression {
        id.asExpression()
    }

    let id: String
    var messages: [ChatMessage]
    var title: String
    var createdTime: Int64
    var isArchived: Bool = false
    var modelName: String = "gpt-4o"
    var stepFormId: String

    init(id: String, messages: [ChatMessage], title: String = "", createdTime: Int64 = Int64(Date().timeIntervalSince1970), isArchived: Bool = false, modelName: String = "gpt-4o", stepFormId: String = "") {
        self.id = id
        self.messages = messages
        self.title = title
        self.createdTime = createdTime
        self.isArchived = isArchived
        self.modelName = modelName
        self.stepFormId = stepFormId
    }

    enum CodingKeys: String, CodingTableKey {
        typealias Root = Chat
        static let objectRelationalMapping = TableBinding(CodingKeys.self) {
            BindColumnConstraint(id, isPrimary: true, isUnique: true)
        }

        case id
        case messages
        case title
        case createdTime
        case isArchived
        case modelName
        case stepFormId
    }

    subscript(id: String) -> ChatMessage? {
        messages.first { $0.id == id }
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}
