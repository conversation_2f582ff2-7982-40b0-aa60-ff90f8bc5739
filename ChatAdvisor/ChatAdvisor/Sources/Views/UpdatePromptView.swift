//
//  UpdatePromptView.swift
//  ChatAdvisor
//
//  版本更新提示视图
//  支持强制更新和可选更新两种模式
//

import SwiftUI

/// 版本更新提示视图
struct UpdatePromptView: View {
    
    // MARK: - 属性
    let versionControl: VersionControl
    let onUpdate: () -> Void
    let onLater: (() -> Void)?
    let onSkip: (() -> Void)?
    
    // MARK: - 计算属性
    
    /// 是否为强制更新
    private var isForceUpdate: Bool {
        versionControl.updateType.isForceUpdate
    }
    
    /// 标题文本
    private var titleText: String {
        if isForceUpdate {
            return "version_update_force_title".localized()
        } else {
            return "version_update_optional_title".localized()
        }
    }
    
    /// 副标题文本
    private var subtitleText: String {
        return String(format: "version_update_subtitle".localized(), versionControl.latestVersion)
    }
    
    // MARK: - 视图主体
    var body: some View {
        ZStack {
            // 背景遮罩（强制更新时不可点击）
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    if !isForceUpdate {
                        onLater?()
                    }
                }
            
            // 主要内容卡片
            VStack(spacing: 0) {
                // 顶部图标区域
                topIconSection
                
                // 内容区域
                contentSection
                
                // 按钮区域
                buttonSection
            }
            .background(Color.white)
            .cornerRadius(16)
            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
            .padding(.horizontal, 40)
        }
    }
    
    // MARK: - 子视图组件
    
    /// 顶部图标区域
    private var topIconSection: some View {
        VStack(spacing: 16) {
            // 应用图标
            if let appIcon = getAppIcon() {
                Image(uiImage: appIcon)
                    .resizable()
                    .frame(width: 80, height: 80)
                    .cornerRadius(16)
            } else {
                // 默认图标
                Image(systemName: "app.badge")
                    .font(.system(size: 60))
                    .foregroundColor(.blue)
            }
            
            // 更新图标指示器
            if isForceUpdate {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(.orange)
            } else {
                Image(systemName: "arrow.up.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(.green)
            }
        }
        .padding(.top, 32)
        .padding(.bottom, 24)
    }
    
    /// 内容区域
    private var contentSection: some View {
        VStack(spacing: 16) {
            // 标题
            Text(titleText)
                .font(.title2)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
                .foregroundColor(.primary)
            
            // 副标题
            Text(subtitleText)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            // 更新消息
            Text(versionControl.updateMessage)
                .font(.body)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 16)
                .lineLimit(nil)
        }
        .padding(.horizontal, 24)
        .padding(.bottom, 32)
    }
    
    /// 按钮区域
    private var buttonSection: some View {
        VStack(spacing: 12) {
            // 立即更新按钮
            Button(action: onUpdate) {
                HStack {
                    Image(systemName: "arrow.down.app")
                        .font(.system(size: 16, weight: .medium))
                    Text("version_update_button_update".localized())
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
            }
            
            // 可选更新的其他按钮
            if !isForceUpdate {
                HStack(spacing: 12) {
                    // 稍后提醒按钮
                    Button(action: { onLater?() }) {
                        Text("version_update_button_later".localized())
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity)
                            .frame(height: 44)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(10)
                    }
                    
                    // 跳过此版本按钮
                    Button(action: { onSkip?() }) {
                        Text("version_update_button_skip".localized())
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity)
                            .frame(height: 44)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(10)
                    }
                }
            }
        }
        .padding(.horizontal, 24)
        .padding(.bottom, 32)
    }
    
    // MARK: - 辅助方法
    
    /// 获取应用图标
    /// - Returns: 应用图标UIImage
    private func getAppIcon() -> UIImage? {
        guard let iconsDictionary = Bundle.main.infoDictionary?["CFBundleIcons"] as? [String: Any],
              let primaryIconsDictionary = iconsDictionary["CFBundlePrimaryIcon"] as? [String: Any],
              let iconFiles = primaryIconsDictionary["CFBundleIconFiles"] as? [String],
              let lastIcon = iconFiles.last else {
            return nil
        }
        
        return UIImage(named: lastIcon)
    }
}

// MARK: - 预览

struct UpdatePromptView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 可选更新预览
            UpdatePromptView(
                versionControl: VersionControl(
                    needUpdate: true,
                    updateType: .optional,
                    latestVersion: "1.2.0",
                    minimumVersion: "1.0.0",
                    updateMessage: "新版本包含了性能优化和错误修复，建议立即更新以获得更好的体验。",
                    downloadUrl: "https://apps.apple.com",
                    versionCheckEnabled: true
                ),
                onUpdate: {},
                onLater: {},
                onSkip: {}
            )
            .previewDisplayName("可选更新")
            
            // 强制更新预览
            UpdatePromptView(
                versionControl: VersionControl(
                    needUpdate: true,
                    updateType: .force,
                    latestVersion: "2.0.0",
                    minimumVersion: "2.0.0",
                    updateMessage: "此版本包含重要的安全更新，必须立即更新才能继续使用应用。",
                    downloadUrl: "https://apps.apple.com",
                    versionCheckEnabled: true
                ),
                onUpdate: {},
                onLater: nil,
                onSkip: nil
            )
            .previewDisplayName("强制更新")
        }
    }
}

// MARK: - 本地化字符串扩展

extension String {
    func localized() -> String {
        return NSLocalizedString(self, comment: "")
    }
}
