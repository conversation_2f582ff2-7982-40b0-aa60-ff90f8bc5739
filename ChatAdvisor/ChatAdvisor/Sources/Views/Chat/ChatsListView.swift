//  ChatsListView.swift
//  JunShi
//
//  Created by md on 2024/5/6.
//

import Foundation
import SwifterSwift
import SwiftUI

struct ChatListView: View {
    @Binding var showSideMenu: Bool
    @Binding var showMultiStepForm: Bool
    @Binding var sideBarWidth: CGFloat
    @Binding var chatViewModels: [ChatViewModel]
    @Binding var selectedChatID: String?
    @EnvironmentObject var contentViewModel: ContentViewModel
    @EnvironmentObject var chatListViewModel: ChatListViewModel
    @FocusState private var isTextFieldFocused: Bool
    @State private var showSettings = false
    @State private var lastSelectionTime = Date()
    @State private var isSelecting = false
    private let selectionDebounceInterval: TimeInterval = 0.3

    var body: some View {
        VStack {
            HStack {
                if AccountManager.shared.currentUser != nil {
                    TextField("搜索聊天".localized(), text: $chatListViewModel.searchText)
                        .padding(AppThemes.padding)
                        .background(Color(UIColor(light: .systemGray4, dark: .systemGray2)))
                        .cornerRadius(8)
                        .padding(.horizontal)
                        .onChange(of: chatListViewModel.searchText) { newValue in
                            if newValue.isEmpty {
                                chatListViewModel.refreshChats()
                            } else {
                                chatListViewModel.searchChats()
                            }
                        }
                        .focused($isTextFieldFocused)
                        .onChange(of: isTextFieldFocused) { isFocused in
                            if isFocused == false {
                                chatListViewModel.refreshChats()
                            }
                            
                            withAnimation {
                                if isFocused {
                                    sideBarWidth = UIScreen.main.bounds.width
                                } else {
                                    sideBarWidth = AppThemes.sideMenuWidth
                                }
                            }
                        }
                }
                if isTextFieldFocused {
                    Button(action: {
                       withAnimation {
                           isTextFieldFocused = false
                       }
                   }) {
                       withAnimation {
                           Image(systemName: "xmark")
                               .foregroundColor(.mainDark)
                       }
                   }
                }
                Spacer()
            }
            .padding(.bottom, AppThemes.padding)

            // 显示加载状态
            if chatListViewModel.isLoading && chatListViewModel.groupedChats.isEmpty {
                VStack {
                    Spacer()
                    ProgressView("正在加载会话...")
                        .tint(.mainDark)
                    Spacer()
                }
            }
            // 显示错误状态
            else if chatListViewModel.hasError {
                VStack {
                    Spacer()
                    Image(systemName: "exclamationmark.triangle")
                        .font(.largeTitle)
                        .foregroundColor(.orange)
                    Text(chatListViewModel.errorMessage)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding()
                    Button("重试") {
                        chatListViewModel.retryRefresh()
                    }
                    .buttonStyle(.borderedProminent)
                    .tint(.mainDark)
                    Spacer()
                }
            }
            // 显示会话列表
            else if chatListViewModel.groupedChats.count > 0 {
                ScrollView {
                    LazyVStack {
                        // 使用 forEach 遍历分组
                        ForEach(Array(chatListViewModel.groupedChats.keys.sorted(by: >)), id: \.self) { date in
                            Section {
                                ForEach(chatListViewModel.groupedChats[date] ?? [], id: \.id) { chat in
                                    Button(action: {
                                        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                                        selectChat(chat)
                                    }) {
                                        HStack {
                                            VStack(alignment: .leading, spacing: 4) {
                                                Text(chat.title != "" ? chat.title : "没有标题的会话".localized())
                                                    .font(.headline)
                                                    .foregroundColor(chat.id == selectedChatID ? .white : .primary)
                                                    .frame(maxWidth: .infinity, alignment: .leading)
                                                    .lineLimit(1)

                                                // 显示最后一条消息的预览
                                                if let lastMessage = chat.messages.last(where: { $0.role == .user || $0.role == .assistant }) {
                                                    Text(lastMessage.content)
                                                        .font(.caption)
                                                        .foregroundColor(chat.id == selectedChatID ? .white.opacity(0.8) : .secondary)
                                                        .lineLimit(1)
                                                }
                                            }

                                            Spacer()

                                            // 选中指示器
                                            if chat.id == selectedChatID {
                                                Image(systemName: "checkmark.circle.fill")
                                                    .foregroundColor(.white)
                                                    .font(.system(size: 16))
                                            }
                                        }
                                    }
                                    .listRowInsets(EdgeInsets())
                                    .listRowSeparator(.hidden)
                                    .contentShape(Rectangle())
                                    .padding(.vertical, 8)
                                    .padding(.horizontal, 12)
                                    .frame(minHeight: 60)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(chat.id == selectedChatID ? Color.accentColor : Color.clear)
                                            .animation(.easeInOut(duration: 0.2), value: selectedChatID)
                                    )
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(chat.id == selectedChatID ? Color.accentColor.opacity(0.3) : Color.clear, lineWidth: 1)
                                    )
                                    .contextMenu {
                                        Button(action: {
                                            chatListViewModel.isRenaming = true
                                            chatListViewModel.newChatTitle = chatListViewModel.getChatTitle(id: chat.id)
                                            chatListViewModel.renamingChatId = chat.id
                                        }) {
                                            Text("重命名".localized())
                                            Image(systemName: "pencil")
                                        }
                                        Button(action: {
                                            chatListViewModel.archiveChat(id: chat.id)
                                        }) {
                                            Text("归档".localized())
                                            Image(systemName: "archivebox")
                                        }
                                        Button(action: {
                                            chatListViewModel.deleteChat(id: chat.id)
                                        }) {
                                            Text("删除".localized())
                                            Image(systemName: "trash")
                                        }
                                    } preview: {
                                        VStack(alignment: .leading) {
                                            if let chatViewModel = chatViewModels.filter({ $0.currentChat.id == chat.id }).first {
                                                if chatViewModel.stepFormViewModel.stepViewModels.filter({ $0.isComplete || $0.isInProgress }).count > 0 {
                                                    MultiStepFormPreviewView()
                                                        .environmentObject(chatViewModel)
                                                        .cornerRadius(10)
                                                }
                                                ChatViewPreview(chat: chat)
                                                Spacer()
                                            }
                                        }
                                        .frame(width: UIScreen.main.bounds.width - 40)
                                        .frame(maxHeight: UIScreen.main.bounds.height * 0.65)
                                        .cornerRadius(10)
                                        .padding()
                                    }
                                }
                                .listRowSeparator(.automatic)
                            } header: {
                                Text(date.chatDateLabel())
                                    .font(.title3)
                                    .foregroundColor(.mainDark)
                            }
                        }
                        // 分页加载指示器
                        if chatListViewModel.searchText.isEmpty {
                            if chatListViewModel.isLoadingMore {
                                HStack {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text("正在加载更多...")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .frame(height: 50)
                                .frame(maxWidth: .infinity)
                            } else if chatListViewModel.loadMore && chatListViewModel.hasMoreData {
                                // 触发加载更多的区域
                                Color.clear
                                    .frame(height: 20)
                                    .onAppear {
                                        chatListViewModel.fetchMoreChats()
                                    }
                            } else if chatListViewModel.showNoMoreDataMessage && !chatListViewModel.chats.isEmpty {
                                Text("已显示全部会话")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .frame(height: 40)
                                    .frame(maxWidth: .infinity)
                            }
                        }
                    }
                }
            } else {
                Spacer()
                Text("empty_session".localized())
                    .foregroundColor(.secondary)
                    .padding(20)
                if UIDevice.current.userInterfaceIdiom == .pad {
                    Spacer()
                } else {
                    Button(action: {
                        if AccountManager.shared.currentUser == nil {
                            withAnimation {
                                AccountManager.shared.needLoggedIn = true
                            }
                            return
                        }
                        showSideMenu.toggle()
                        showMultiStepForm.toggle()
                    }) {
                        HStack {
                            Spacer()
                            Image(systemName: "plus")
                                .foregroundColor(.mainDark)
                            Text("开始新的对话吧".localized())
                                .foregroundColor(.mainDark)
                            Spacer()
                        }
                        .padding()
                    }
                    Spacer()
                }
            }

            Divider()
            HStack {
                HStack {
                    Spacer()
                    Text(AccountManager.shared.currentUser?.fullName ?? AccountManager.shared.currentUser?.email.emailPrefix ?? "Guest".localized())
                        .padding(.horizontal, AppThemes.padding)
                        .foregroundColor(.mainDark)
                    Spacer()
                }
                Image(systemName: "gearshape")
                    .frame(width: 44, height: 44)
                    .padding(.horizontal, AppThemes.padding)
                    .foregroundColor(.mainDark)
            }
            .onTapGesture {
                UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                showSettings = true
            }
            .padding(.horizontal)
        }
        .onChange(of: AccountManager.shared.currentUser) { new in
            if new != nil {
                chatListViewModel.fetchMoreChats()
            }
        }
        .onAppear {
            // 确保视图出现时加载数据，但避免重复加载
            if AccountManager.shared.currentUser != nil && !chatListViewModel.isInitialLoadCompleted {
                chatListViewModel.performInitialLoad()
            }
        }
        .alert("重命名".localized(), isPresented: $chatListViewModel.isRenaming) {
            TextField(chatListViewModel.newChatTitle, text: $chatListViewModel.newChatTitle)
            Button("取消", role: .cancel) {
                chatListViewModel.isRenaming = false
            }
            Button("好的".localized()) {
                chatListViewModel.renameChat()
            }
        } message: {
            Text("请输入新的聊天标题".localized())
        }
        .sheet(isPresented: $showSettings) {
            NavigationView {
                SettingsView(showSettings: $showSettings)
                    .navigationBarTitleDisplayMode(.inline)
            }
        }
    }

    // MARK: - Helper Methods

    private func selectChat(_ chat: Chat) {
        // 防抖机制：防止频繁点击
        let now = Date()
        guard now.timeIntervalSince(lastSelectionTime) > selectionDebounceInterval else { return }
        lastSelectionTime = now

        // 防止重复选择同一个会话
        guard selectedChatID != chat.id else { return }

        // 防止并发选择
        guard !isSelecting else { return }
        isSelecting = true

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        Task {
            do {
                // 设置加载状态
                await MainActor.run {
                    contentViewModel.isLoadingChatDetails = true
                }

                // 使用原子化的会话选择方法
                try? await contentViewModel.selectChatAtomically(chat: chat, chatListViewModel: chatListViewModel)

                // 确保chatViewModels绑定是最新的
                await MainActor.run {
                    if let chatViewModel = contentViewModel.currentChatViewModel,
                       !chatViewModels.contains(where: { $0.currentChat.id == chat.id }) {
                        chatViewModels.append(chatViewModel)
                    }

                    // 移除重复的selectedChatID设置，因为selectChatAtomically已经处理了
                    // 使用动画来提供视觉反馈
                    withAnimation(.easeInOut(duration: 0.2)) {
                        // 动画会自动触发UI更新，无需手动调用objectWillChange
                    }

                    contentViewModel.isLoadingChatDetails = false
                    isSelecting = false

                    // 移除自动关闭侧边菜单的逻辑，让用户可以连续切换多个会话
                    // 用户可以手动点击菜单按钮或点击空白区域来关闭菜单
                }

            } catch {
                // 错误处理
                await MainActor.run {
                    contentViewModel.isLoadingChatDetails = false
                    isSelecting = false

                    // 设置详细的错误信息
                    contentViewModel.chatLoadingError = "选择会话失败: \(error.localizedDescription)"
                    chatListViewModel.hasError = true
                    chatListViewModel.errorMessage = "切换会话失败，请重试"

//                    logger.error("选择会话失败: \(error)")
                }
            }
        }
    }
}
