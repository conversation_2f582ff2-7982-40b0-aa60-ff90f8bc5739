//
//  MultiStepFormViewModel.swift
//  test
//
//  Created by md on 2024/7/5.
//

import Combine
import Foundation
import Localize_Swift
import SwiftUI
import WCDBSwift

final class MultiStepFormViewModel: ObservableObject, Codable {
    var chatId: String
    var stepFormId: String
    weak var recognizeViewModel: RecognizeViewModel?
    func generateTitle() -> [String] {
        var nonEmptyFields: [String] = []
        for stepViewModel in stepViewModels {
            for (key, value) in stepViewModel.fields {
                if !value.isEmpty {
                    nonEmptyFields.append("\(key.localized()):\(value.localized())")
                    break
                }
            }
            if !nonEmptyFields.isEmpty {
                break
            }
            for (key, value) in stepViewModel.customFields {
                if !value.isEmpty {
                    nonEmptyFields.append("\(key.localized()):\(value.localized())")
                    break
                }
            }
        }
        return nonEmptyFields
    }

    var title: String {
        let nonEmptyFields = generateTitle()
        return nonEmptyFields.isEmpty ? "没有标题的会话".localized() : nonEmptyFields.joined(separator: ", ")
    }

    var navigationtitle: String {
        let nonEmptyFields = generateTitle()
        return nonEmptyFields.isEmpty ? "" : "\(nonEmptyFields.joined(separator: ", "))"
    }

    @Published var isLocalRecognition = true
    @Published var isProcessing = false
    @Published var disableComplete = false
    @Published var isEditing = false
    @Published var currentStep = 4 {
        didSet {
            // hide keyboard
            DispatchQueue.main.async {
                UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
            }
        }
    }

    @Published var stepViewModels: [StepViewModel] {
        didSet {
            cancellables.forEach { $0.cancel() }
            for stepViewModel in stepViewModels {
                stepViewModel.objectWillChange
                    .sink { [weak self] _ in
                        self?.objectWillChange.send()
                    }
                    .store(in: &cancellables)
            }
        }
    }

    @Published var options: [StepOption] = []
    private var cancellables = Set<AnyCancellable>()

    private var originalSteps: [Step] = []
    private var originalStepViewModels: [StepViewModel] = []

    // 初始化或其他方法中保存初始状态
    func backupState() {
        originalStepViewModels = stepViewModels.compactMap { $0.deepCopy() }
    }

    func restoreState() {
        stepViewModels = originalStepViewModels
    }

    // Codable
    required init(stepFormId: String, chatId: String, stepViewModels: [StepViewModel] = []) {
        self.stepViewModels = stepViewModels
        self.chatId = chatId
        self.stepFormId = stepFormId
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(currentStep, forKey: .currentStep)
        try container.encode(stepViewModels, forKey: .stepViewModels)
        try container.encode(chatId, forKey: .chatId)
        try container.encode(stepFormId, forKey: .stepFormId)
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        currentStep = try container.decode(Int.self, forKey: .currentStep)
        stepViewModels = try container.decode([StepViewModel].self, forKey: .stepViewModels)
        chatId = try container.decode(String.self, forKey: .chatId)
        stepFormId = try container.decode(String.self, forKey: .stepFormId)
    }

    func initailData() -> MultiStepFormViewModel {
        stepViewModels = []
        loadOptionsFromDatabase()
        for stepType in StepType.allCases {
            let stepViewModel = StepViewModel(chatId: chatId)
            stepViewModel.stepFormViewModel = self
            stepViewModel.stepType = stepType
            stepViewModel.stepFormId = stepFormId

            for field in stepType.fields {
                stepViewModel.setField(field, value: "")
            }

            for optionKey in stepType.predefinedOptions {
                stepViewModel.setPredefinedOptions(optionKey, options: getOptions(for: optionKey))
            }

            if stepType == .custom {
                stepViewModel.showCustomFields = true
            }

            if stepType == .import {
                stepViewModel.showCustomFields = false
            }

            stepViewModels.append(stepViewModel)
        }

        return self
    }

    var steps: [Step] {
        stepViewModels
    }

    func loadOptionsFromDatabase() {
        options = ChatConfigDatabaseManager.shared.loadOptionsFromDatabase()
    }

    private func getOptions(for key: String) -> [String] {
        options.filter { $0.key == key }.map(\.value)
    }

    func saveToDatabase() {
        ChatConfigDatabaseManager.shared.saveMultiStepFormViewModelToDatabase(self)
    }

    func loadFromDatabase() {
        if let multiStepFormViewModel = ChatConfigDatabaseManager.shared.loadMultiStepFormViewModelFromDatabase(chatId: chatId) {
            stepFormId = multiStepFormViewModel.stepFormId
            currentStep = multiStepFormViewModel.currentStep
            stepViewModels = multiStepFormViewModel.stepViewModels
            for stepViewModel in stepViewModels {
                stepViewModel.stepFormId = stepFormId
                for optionKey in stepViewModel.stepType.predefinedOptions {
                    stepViewModel.setPredefinedOptions(optionKey, options: getOptions(for: optionKey))
                }
            }
        }
    }

    func generatePrompt() -> String {
        var userInfo: [String: String] = [:]
        var chatPreferences: [String: String] = [:]
        var emotionAndAttitude: [String: String] = [:]
        var customFields: [String: String] = [:]

        for stepViewModel in stepViewModels {
            switch stepViewModel.stepType {
            case .information:
                for (key, value) in stepViewModel.fields where !value.isEmpty {
                    userInfo[key] = value
                }

            case .preferences:
                for (key, value) in stepViewModel.fields where !value.isEmpty {
                    chatPreferences[key] = value
                }

            case .emotion:
                for (key, value) in stepViewModel.fields where !value.isEmpty {
                    emotionAndAttitude[key] = value
                }

            case .custom:
                for (key, value) in stepViewModel.customFields where !value.isEmpty {
                    customFields[key] = value
                }

            case .import:
                break
            default:
                break
            }
        }

        var prompt = ""
//        Localize.setCurrentLanguage("en")

//        if isLocalRecognition {
//            prompt = """
//            \(Configs.default.promotLocal ?? "promot_local".localized(in: "en")),
//            """
//        } else {
//            prompt = """
//            \(Configs.default.promotCloud ?? "promot_cloud".localized()),
//            """
//        }
//        Localize.resetCurrentLanguageToDefault()

        if !userInfo.isEmpty {
//            prompt += "### \("Information".localized())\n"
            for (key, value) in userInfo {
                prompt += "\(key.localized()): \(value.localized()),"
            }
        }

        if !chatPreferences.isEmpty {
//            prompt += "\n### \("Preferences".localized())\n"
            for (key, value) in chatPreferences {
                prompt += "\(key.localized()): \(value.localized()),"
            }
        }

        if !emotionAndAttitude.isEmpty {
//            prompt += "\n### \("Emotion".localized())\n"
            for (key, value) in emotionAndAttitude {
                prompt += "\(key.localized()): \(value),"
            }
        }

        if !customFields.isEmpty {
//            prompt += "\n### \("Custom".localized())\n"
            for (key, value) in customFields {
                prompt += "\(key.localized()): \(value),"
            }
        }

//        for content in BootManager.shared.config.hideMessage {
//            prompt += "\(content),"
//        }

        return prompt
    }
}

extension MultiStepFormViewModel: TableCodable {
    enum CodingKeys: String, CodingTableKey {
        typealias Root = MultiStepFormViewModel
        case chatId
        case stepFormId
        case currentStep
        case stepViewModels

        static let objectRelationalMapping = TableBinding(CodingKeys.self) {
            BindColumnConstraint(chatId, isPrimary: true)
        }
    }
}
