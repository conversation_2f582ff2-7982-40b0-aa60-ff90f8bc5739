import Foundation
import SimpleToast
import SwifterSwift
import SwiftUI
import UIKit

// 用于存储不同状态和模式的颜色
enum AppThemes {
    static var primaryColor: UIColor = Preferences.themeColor.value {
        didSet {
            Preferences.themeColor.value = AppThemes.primaryColor
        }
    } // 默认主题颜色
    static var colors: ColorStates {
        ColorStates(baseColor: primaryColor)
    }

    static var padding: CGFloat = 12
    static var cornerRadius: CGFloat = 12
    static var toastOptions = SimpleToastOptions(
        alignment: .top,
        hideAfter: 6
    )
    
    // 新增按钮样式
    static var buttonCornerRadius: CGFloat = 8
    static var buttonHeight: CGFloat = 44
    static var buttonMaxWidth: CGFloat = 375
    static var fontSize = 16.0
    static var buttonDisabledOpacity: Double = 0.5

    
    static var isDarkMode: Bool {
        UIScreen.main.traitCollection.userInterfaceStyle == .dark
    }

    static var sideMenuWidth: CGFloat {
        if UIDevice.current.isPad {
            UIScreen.main.bounds.width / 2
        } else {
            UIScreen.main.bounds.width - 120
        }
    }

    static var animationFont: Font {
        // 如果是中文,大一些
        if Locale.current.language.languageCode?.identifier == "zh" || UIDevice.current.isPad {
            .system(size: 24, weight: .bold, design: .default)
        } else {
            .system(size: 17, weight: .semibold, design: .default)
        }
    }
}

// 用于存储不同状态和模式的颜色
struct ColorStates {
    let mainBackground: UIColor
    let mainDark: UIColor
    let mainLight: UIColor
    let mainHighlighted: UIColor
    let mainDisabled: UIColor

    let backgroundReverse: UIColor

    init(baseColor: UIColor) {
        // 获取当前用户界面样式
        let userInterfaceStyle = UIScreen.main.traitCollection.userInterfaceStyle
        let isDarkMode = userInterfaceStyle == .dark

        // 根据模式调整基色
        let adjustedBaseColor = baseColor

        // 根据不同模式生成背景颜色
        mainBackground = isDarkMode ? adjustedBaseColor.lighten(by: 0.2) : adjustedBaseColor.darken(by: 0.2)
        mainDark = isDarkMode ? adjustedBaseColor.lighten(by: 0.2) : adjustedBaseColor.darken(by: 0.2)
        mainLight = isDarkMode ? adjustedBaseColor.darken(by: 0.2) : adjustedBaseColor.lighten(by: 0.2)
        mainHighlighted = isDarkMode ? adjustedBaseColor.lighten(by: 0.15) : adjustedBaseColor.darken(by: 0.15)
        mainDisabled = isDarkMode ? adjustedBaseColor.lighten(by: 0.3) : adjustedBaseColor.darken(by: 0.3)

        // 文字颜色是基于亮度决定，以确保足够的对比度
        backgroundReverse = isDarkMode ? .black : .white
    }
}

extension Color {
    static var mainBackground: Color { Color(AppThemes.colors.mainBackground) }
    static var mainDark: Color { Color(AppThemes.colors.mainDark) }
    static var mainLight: Color { Color(AppThemes.colors.mainLight) }
    static var mainHighlighted: Color { Color(AppThemes.colors.mainHighlighted) }
    static var mainDisabled: Color { Color(AppThemes.colors.mainDisabled) }

    static var reverse: Color { Color(AppThemes.colors.backgroundReverse) }

    var uiColor: UIColor {
        UIColor(self)
    }
}
