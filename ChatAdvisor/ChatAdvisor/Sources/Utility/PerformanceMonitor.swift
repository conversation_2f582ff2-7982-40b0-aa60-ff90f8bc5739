//
//  PerformanceMonitor.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/02.
//

import Foundation
import Combine
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "PerformanceMonitor")

/// 性能监控器 - 监控应用关键操作的性能指标
class PerformanceMonitor: ObservableObject {
    static let shared = PerformanceMonitor()
    
    // MARK: - Published Properties
    
    @Published private(set) var metrics: [PerformanceMetric] = []
    @Published private(set) var alerts: [PerformanceAlert] = []
    @Published private(set) var isMonitoring: Bool = false
    
    // MARK: - Private Properties
    
    private var activeOperations: [String: Date] = [:]
    private let maxMetricsCount = 1000
    private var cancellables = Set<AnyCancellable>()
    
    // 性能阈值
    private let thresholds = PerformanceThresholds()
    
    // MARK: - Initialization
    
    private init() {
        setupPeriodicCleanup()
    }
    
    private func setupPeriodicCleanup() {
        Timer.publish(every: 300, on: .main, in: .common) // 每5分钟清理一次
            .autoconnect()
            .sink { [weak self] _ in
                self?.cleanupOldMetrics()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 开始监控
    func startMonitoring() {
        isMonitoring = true
        logger.info("性能监控已启动")
    }
    
    /// 停止监控
    func stopMonitoring() {
        isMonitoring = false
        activeOperations.removeAll()
        logger.info("性能监控已停止")
    }
    
    /// 开始记录操作
    func startOperation(_ operationId: String, type: OperationType) {
        guard isMonitoring else { return }
        
        activeOperations[operationId] = Date()
        logger.debug("开始记录操作: \(operationId), 类型: \(type.rawValue)")
    }
    
    /// 结束记录操作
    func endOperation(_ operationId: String, type: OperationType, success: Bool = true, error: Error? = nil) {
        guard isMonitoring else { return }
        guard let startTime = activeOperations.removeValue(forKey: operationId) else {
            logger.warning("未找到操作开始时间: \(operationId)")
            return
        }
        
        let duration = Date().timeIntervalSince(startTime)
        let metric = PerformanceMetric(
            id: UUID().uuidString,
            operationId: operationId,
            type: type,
            duration: duration,
            timestamp: Date(),
            success: success,
            error: error?.localizedDescription
        )
        
        addMetric(metric)
        checkThresholds(metric)
        
        logger.debug("操作完成: \(operationId), 耗时: \(String(format: "%.3f", duration))秒")
    }
    
    /// 记录自定义指标
    func recordCustomMetric(name: String, value: Double, unit: String = "") {
        guard isMonitoring else { return }
        
        let metric = PerformanceMetric(
            id: UUID().uuidString,
            operationId: name,
            type: .custom,
            duration: value,
            timestamp: Date(),
            success: true,
            error: nil,
            customUnit: unit
        )
        
        addMetric(metric)
    }
    
    /// 获取性能报告
    func generateReport(for timeRange: TimeInterval = 3600) -> PerformanceReport {
        let cutoffTime = Date().addingTimeInterval(-timeRange)
        let recentMetrics = metrics.filter { $0.timestamp >= cutoffTime }
        
        return PerformanceReport(
            timeRange: timeRange,
            totalOperations: recentMetrics.count,
            successRate: calculateSuccessRate(recentMetrics),
            averageDurations: calculateAverageDurations(recentMetrics),
            slowestOperations: findSlowestOperations(recentMetrics),
            errorSummary: generateErrorSummary(recentMetrics),
            alerts: alerts.filter { $0.timestamp >= cutoffTime }
        )
    }
    
    /// 清除所有数据
    func clearData() {
        metrics.removeAll()
        alerts.removeAll()
        activeOperations.removeAll()
    }
    
    // MARK: - Private Methods
    
    private func addMetric(_ metric: PerformanceMetric) {
        metrics.append(metric)
        
        // 限制指标数量
        if metrics.count > maxMetricsCount {
            metrics.removeFirst(metrics.count - maxMetricsCount)
        }
    }
    
    private func checkThresholds(_ metric: PerformanceMetric) {
        let threshold = thresholds.threshold(for: metric.type)
        
        if metric.duration > threshold {
            let alert = PerformanceAlert(
                id: UUID().uuidString,
                type: .slowOperation,
                message: "操作 \(metric.operationId) 耗时 \(String(format: "%.3f", metric.duration))秒，超过阈值 \(threshold)秒",
                severity: metric.duration > threshold * 2 ? .critical : .warning,
                timestamp: Date(),
                relatedMetric: metric
            )
            
            alerts.append(alert)
            logger.warning("性能告警: \(alert.message)")
        }
        
        if !metric.success {
            let alert = PerformanceAlert(
                id: UUID().uuidString,
                type: .operationFailure,
                message: "操作 \(metric.operationId) 失败: \(metric.error ?? "未知错误")",
                severity: .error,
                timestamp: Date(),
                relatedMetric: metric
            )
            
            alerts.append(alert)
        }
    }
    
    private func cleanupOldMetrics() {
        let cutoffTime = Date().addingTimeInterval(-86400) // 保留24小时的数据
        metrics.removeAll { $0.timestamp < cutoffTime }
        alerts.removeAll { $0.timestamp < cutoffTime }
    }
    
    private func calculateSuccessRate(_ metrics: [PerformanceMetric]) -> Double {
        guard !metrics.isEmpty else { return 0 }
        let successCount = metrics.filter { $0.success }.count
        return Double(successCount) / Double(metrics.count)
    }
    
    private func calculateAverageDurations(_ metrics: [PerformanceMetric]) -> [OperationType: Double] {
        var result: [OperationType: Double] = [:]
        let groupedMetrics = Dictionary(grouping: metrics) { $0.type }
        
        for (type, typeMetrics) in groupedMetrics {
            let totalDuration = typeMetrics.reduce(0) { $0 + $1.duration }
            result[type] = totalDuration / Double(typeMetrics.count)
        }
        
        return result
    }
    
    private func findSlowestOperations(_ metrics: [PerformanceMetric]) -> [PerformanceMetric] {
        return metrics.sorted { $0.duration > $1.duration }.prefix(10).map { $0 }
    }
    
    private func generateErrorSummary(_ metrics: [PerformanceMetric]) -> [String: Int] {
        let failedMetrics = metrics.filter { !$0.success }
        return Dictionary(grouping: failedMetrics) { $0.error ?? "未知错误" }
            .mapValues { $0.count }
    }
}

// MARK: - Supporting Types

/// 操作类型
enum OperationType: String, CaseIterable {
    case sessionSwitch = "会话切换"
    case messageLoad = "消息加载"
    case databaseQuery = "数据库查询"
    case networkRequest = "网络请求"
    case uiRender = "UI渲染"
    case custom = "自定义"
}

/// 性能指标
struct PerformanceMetric {
    let id: String
    let operationId: String
    let type: OperationType
    let duration: TimeInterval
    let timestamp: Date
    let success: Bool
    let error: String?
    let customUnit: String?
    
    init(id: String, operationId: String, type: OperationType, duration: TimeInterval, timestamp: Date, success: Bool, error: String?, customUnit: String? = nil) {
        self.id = id
        self.operationId = operationId
        self.type = type
        self.duration = duration
        self.timestamp = timestamp
        self.success = success
        self.error = error
        self.customUnit = customUnit
    }
}

/// 性能告警
struct PerformanceAlert {
    let id: String
    let type: AlertType
    let message: String
    let severity: AlertSeverity
    let timestamp: Date
    let relatedMetric: PerformanceMetric
    
    enum AlertType {
        case slowOperation
        case operationFailure
        case memoryWarning
        case networkIssue
    }
    
    enum AlertSeverity {
        case info
        case warning
        case error
        case critical
    }
}

/// 性能阈值配置
struct PerformanceThresholds {
    private let thresholds: [OperationType: TimeInterval] = [
        .sessionSwitch: 1.0,    // 会话切换应在1秒内完成
        .messageLoad: 0.5,      // 消息加载应在0.5秒内完成
        .databaseQuery: 0.3,    // 数据库查询应在0.3秒内完成
        .networkRequest: 5.0,   // 网络请求应在5秒内完成
        .uiRender: 0.1,         // UI渲染应在0.1秒内完成
        .custom: 1.0            // 自定义操作默认阈值
    ]
    
    func threshold(for type: OperationType) -> TimeInterval {
        return thresholds[type] ?? 1.0
    }
}

/// 性能报告
struct PerformanceReport {
    let timeRange: TimeInterval
    let totalOperations: Int
    let successRate: Double
    let averageDurations: [OperationType: Double]
    let slowestOperations: [PerformanceMetric]
    let errorSummary: [String: Int]
    let alerts: [PerformanceAlert]
    
    var description: String {
        let timeRangeHours = timeRange / 3600
        let successRatePercent = successRate * 100
        
        var report = """
        性能报告 (过去 \(String(format: "%.1f", timeRangeHours)) 小时):
        - 总操作数: \(totalOperations)
        - 成功率: \(String(format: "%.1f", successRatePercent))%
        - 告警数: \(alerts.count)
        
        平均耗时:
        """
        
        for (type, duration) in averageDurations {
            report += "\n- \(type.rawValue): \(String(format: "%.3f", duration))秒"
        }
        
        if !slowestOperations.isEmpty {
            report += "\n\n最慢操作:"
            for (index, metric) in slowestOperations.prefix(5).enumerated() {
                report += "\n\(index + 1). \(metric.operationId): \(String(format: "%.3f", metric.duration))秒"
            }
        }
        
        if !errorSummary.isEmpty {
            report += "\n\n错误统计:"
            for (error, count) in errorSummary {
                report += "\n- \(error): \(count)次"
            }
        }
        
        return report
    }
}
