//
//  AppDelegate.swift
//  ChatAdvisor
//
//  Created by md on 2024/11/7.
//

import Foundation
#if !DEBUG
    import Bugly
    import StoreKit
#endif
import AdSupport
import Firebase
import FirebaseCrashlytics
import FirebaseMessaging
import GoogleSignIn
import Network

class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil) -> Bool {
        #if !DEBUG
//        if SKPaymentQueue.default().storefront?.countryCode == "CHN" {
//
//        } else {
//
//        }
            Bugly.start(withAppId: "71953be5e0")
            FirebaseApp.configure()
            FirebaseConfiguration.shared.setLoggerLevel(.min)
        #endif

        if Preferences.hasLaunchedBefore.value == false {
            monitorNetworkStatus()
            Preferences.hasLaunchedBefore.value = true
        }

        // 通知注册
        UNUserNotificationCenter.current().delegate = self

        let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
        UNUserNotificationCenter.current().requestAuthorization(
            options: authOptions,
            completionHandler: { _, _ in
            }
        )

        application.registerForRemoteNotifications()
        Messaging.messaging().delegate = self

        #if DEBUG
//        Installations.installations().installationID { identifier, error in
//          guard let installationID = identifier else {
//            print("Error getting installation ID: \(error!)")
//              return
//          }
//
//
//          print("Firebase installations ID = \(installationID)")
//        }
        #endif
        BootManager.shared.getConfig()
        BootManager.shared.getPricing()
        ShopManager.shared.getProducts()

        if AccountManager.shared.currentUser != nil || AccountManager.shared.isLoggedIn {
            AccountManager.shared.afterLogin()
            AccountManager.shared.refreshTokenIfNeed()

            // 预初始化数据库连接，减少启动时的加载时间
            Task {
                _ = AdvisorDatabaseManager.shared.database
            }
        }
    
        return true
    }

    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> Bool {
        if url.scheme == "chatadvisor" {
            return true
        }
        
        // 处理Twitter登录回调
        if url.scheme == "twitterkit-\(AuthViewModel.shared.twitterApiKey)" {
            return AuthViewModel.shared.twitterApplication(open: url)
        }

        // 处理Google登录回调
        let googleHandled = GIDSignIn.sharedInstance.handle(url)

        return googleHandled
    }

    func applicationWillTerminate(_: UIApplication) {
        Preferences.didCrashInLastSession.value = false
    }
}

extension AppDelegate: UNUserNotificationCenterDelegate {
    func userNotificationCenter(_: UNUserNotificationCenter,
                                willPresent notification: UNNotification,
                                withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void)
    {
        let userInfo = notification.request.content.userInfo

        Messaging.messaging().appDidReceiveMessage(userInfo)
        completionHandler([.banner, .sound, .badge, .list])
    }

    func userNotificationCenter(_: UNUserNotificationCenter,
                                didReceive response: UNNotificationResponse,
                                withCompletionHandler completionHandler: @escaping () -> Void)
    {
        let userInfo = response.notification.request.content.userInfo

        Messaging.messaging().appDidReceiveMessage(userInfo)
        completionHandler()
    }

    func application(_: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        Messaging.messaging().apnsToken = deviceToken
    }

    func application(_: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("Failed to register for remote notifications with error: \(error)")
    }

    func application(_: UIApplication,
                     didReceiveRemoteNotification userInfo: [AnyHashable: Any],
                     fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void)
    {
        Messaging.messaging().appDidReceiveMessage(userInfo)
        completionHandler(.noData)
    }
}

extension AppDelegate: MessagingDelegate {
    func messaging(_: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        guard let fcmToken else { return }
        Messaging.messaging().token { token, error in
            if let error {
                print("Error fetching FCM registration token: \(error)")
            } else if let token {
                print("FCM registration token: \(token)")
            }
        }
    }
}

extension AppDelegate {
    func monitorNetworkStatus() {
        let monitor = NWPathMonitor()
        let queue = DispatchQueue.global(qos: .background)
        monitor.start(queue: queue)

        monitor.pathUpdateHandler = { path in
            if path.status == .satisfied {
                BootManager.shared.getConfig()
                BootManager.shared.getPricing()
                ShopManager.shared.getProducts()
            } else {
                print("No connection.")
            }

            if path.usesInterfaceType(.wifi) {
                print("WiFi connection.")
            } else if path.usesInterfaceType(.cellular) {
                print("Cellular connection.")
            } else if path.usesInterfaceType(.wiredEthernet) {
                print("Ethernet connection.")
            } else if path.usesInterfaceType(.loopback) {
                print("Loopback connection.")
            } else if path.usesInterfaceType(.other) {
                print("Other connection.")
            }
        }
    }
}
